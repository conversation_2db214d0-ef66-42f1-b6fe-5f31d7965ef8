<template>
	<div class="numRange">
		<div>{{ title }}</div>
		<el-select v-model="operator" placeholder="操作符" @change="changeOperator" clearable class="operatorSelect">
			<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
		</el-select>
		<el-input-number class="numberInput" v-model="minNum" :min="props.min" :max="props.max" :controls="false" :precision="props.precision" :placeholder="props.minPlaceHolder" />
		<div v-if="operator === 4">-</div>
		<el-input-number
			class="numberInput"
			v-if="operator === 4"
			v-model="maxNum"
			:min="props.min"
			:max="props.max"
			:controls="false"
			:precision="props.precision"
			:placeholder="props.maxPlaceHolder"
		/>
	</div>
</template>

<script setup lang="ts" name="">
import { ref, defineModel, defineProps } from 'vue';
const props = defineProps({
	max: {
		type: Number,
		default: 10000000000,
	},
	min: {
		type: Number,
		default: -10000000,
	},
	precision: {
		type: Number,
		default: 0,
	},
	minPlaceHolder: {
		type: String,
		default: '最小值',
	},
	maxPlaceHolder: {
		type: String,
		default: '最大值',
	},
	title: {
		type: String,
		default: '',
	},
});
const options = ref([
	{ label: '大于', value: 1 },
	{ label: '小于', value: 3 },
	{ label: '等于', value: 2 },
	{ label: '介于', value: 4 },
]);
const minNum = defineModel('minNum');
const maxNum = defineModel('maxNum');
const operator = defineModel('operator');

const changeOperator = (e: number | null) => {
	if (!e) {
		minNum.value = null;
		maxNum.value = null;
	} else if (e !== 4) {
		maxNum.value = null;
	}
};
</script>

<style scoped lang="scss">
.numRange {
	display: flex;
	align-items: center;
	font-size: 12px;

	.operatorSelect {
		width: 70px;
		margin: 0 5px;
	}

	.numberInput {
		width: 100px;
	}
}
</style>
