<template>
    <div class="home-content">
        <h1 class="title">欢迎来到 <span class="highlight">1688选品中心ERP</span> 系统</h1>
        <div class="content_card">
            <div class="cart_item" v-for="(item, i) in cardList" :key="item.title" @click="linkToPage(item.path)">
                <el-icon size="40" color="#64c5b1" v-show="i == 0">
                    <Document />
                </el-icon>
                <el-icon size="40" color="#64c5b1" v-show="i == 1">
                    <Discount />
                </el-icon>
                <el-icon size="40" color="#64c5b1" v-show="i == 2">
                    <House />
                </el-icon>
                <el-icon size="40" color="#64c5b1" v-show="i == 3">
                    <Money />
                </el-icon>
                <el-icon size="40" color="#64c5b1" v-show="i == 4">
                    <DataAnalysis />
                </el-icon>
                <!-- 默认图标 -->
                <el-icon size="40" color="#64c5b1" v-show="i > 4">
                    <DataBoard />
                </el-icon>
                <div>{{ item.label }}</div>
            </div>

        </div>
    </div>
</template>
<script setup lang="ts">
import judgePort from '/@/utils/judgePort';
import request from '/@/utils/net.js';
import { ref, onMounted } from 'vue';
import { useUserInfo } from '/@/stores/userInfo';
import { useRouter } from 'vue-router';
const userInfos = useUserInfo().userInfos
const cardList = ref<any>([]);
const router = useRouter();
const getMenu = (menu: Array<any>) => {
    menu.forEach((item: any) => {
        if (item.children && item.children.length > 0) {
            getMenu(item.children);
        } else {
            cardList.value.push(item);
        }
    })
}
const linkToPage = (path: string) => {
    router.push({
        path
    })
}
onMounted(async () => {
    const menu = userInfos.menus.find((a: any) => a.parentId == 0 && !a.hidden && a.label == '选品中心');
    const menus = userInfos.menus.filter((a: any) => a.parentId == menu.id && !a.hidden).filter((item: any) => item.label != '首页')
    getMenu(menus);
})
</script>

<style lang="scss" scoped>
.home-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #f2f2f2;

    .content_card {
        width: 80%;
        height: 400px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        font-size: 20px;
        color: #000;
        font-weight: 700;
        display: flex;
        flex-wrap: wrap;
        gap: 20px;

        .cart_item {
            cursor: pointer;
            width: 300px;
            height: 150px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            gap: 20px;
        }
    }
}

.title {
    text-align: center;
    font-size: 28px;
    color: #333;
    font-weight: bold;

    .highlight {
        color: #64c5b1;
    }
}
</style>