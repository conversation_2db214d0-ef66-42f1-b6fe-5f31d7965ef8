<template>
	<div class="w100 h100">
		<Container>
			<template #header>
				<div class="topCss">
					<el-input v-model="query.keyWords" placeholder="关键字查询,公司名称等" class="publicCss" style="width: 230px"
						maxlength="200" clearable />
					<!-- <dataRange class="publicCss" v-model:startDate="query.startFirstGuestTime"
						v-model:endDate="query.endFirstGuestTime" style="width: 230px" start-placeholder="首次到访时间"
						end-placeholder="首次到访时间" /> -->
					<dataRange class="publicCss" v-model:startDate="query.startLastGuestTime"
						v-model:endDate="query.endLastGuestTime" style="width: 230px" start-placeholder="到访时间"
						end-placeholder="到访时间" />
					<el-select v-model="query.status" placeholder="状态" class="publicCss" clearable>
						<el-option v-for="item in statusList" :key="item.value" :label="item.label"
							:value="item.value" />
					</el-select>
					<el-select v-model="query.guestType" placeholder="来宾类型" class="publicCss" clearable>
						<el-option v-for="item in guestTypeList" :key="item.value" :label="item.label"
							:value="item.value" />
					</el-select>
					<div class="pb5">
						<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
					</div>
				</div>
			</template>
			<template #content>
				<vxetable ref="table" id="20250611103922" :tableCols="tableCols" order-by="addTime" :is-asc="false"
					showsummary :query="query" :query-api="GetCrmGuestList" :export-api="ExportPremiumDeduction"
					:asyncExport="{
						title: '1688展厅来访',
						isAsync: false
					}">
					<template #toolbar_buttons>
						<el-button type="primary" @click="visitorRegistration(true, null)">访客登记</el-button>
					</template>
				</vxetable>
			</template>
		</Container>

		<el-dialog draggable overflow v-model="recordsVisible" title="访客记录" width="60%" :close-on-click-modal="false"
			:before-close="close">
			<visitorRecords v-if="recordsVisible" style="height: 700px" :fkId="fkId" @close="close" />
		</el-dialog>

		<el-dialog draggable overflow v-model="addVisitorVisible" :title="isAdd ? '新增访客' : '编辑访客'" width="60%"
			:close-on-click-modal="false">
			<addVisitor v-if="addVisitorVisible" :info="fkForm" @close="close" />
		</el-dialog>

		<el-dialog draggable overflow v-model="returnVisible" title="跟进记录" width="60%" :close-on-click-modal="false"
			:before-close="close">
			<returnRecords v-if="returnVisible" style="height: 700px" :hfId="hfId" @close="close" />
		</el-dialog>

		<el-drawer v-model="drawer" title="预览附件" direction="btt" size="80%">
			<VueOfficeExcel :src="excelUrl" style="height: 100%;" />
		</el-drawer>

	</div>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent } from 'vue';
import { GetCrmGuestList, ExportPremiumDeduction } from '/@/api/customerservice/customerGroup';
import { downLoadFile } from '/@/utils/tools'
import VueOfficeExcel from '@vue-office/excel/lib/v3/vue-office-excel.mjs'
import '@vue-office/excel/lib/v3/index.css'
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const visitorRecords = defineAsyncComponent(() => import('./components/visitorRecords.vue'));
const returnRecords = defineAsyncComponent(() => import('./components/returnRecords.vue'));
const addVisitor = defineAsyncComponent(() => import('./components/addVisitor.vue'));
const recordsVisible = ref(false);
const returnVisible = ref(false);
const drawer = ref(false);
const addVisitorVisible = ref(false);
const query = ref({
	keyWords: '',
	startFirstGuestTime: '',
	endFirstGuestTime: '',
	startLastGuestTime: '',
	endLastGuestTime: '',
	startFirstAddTime: '',
	endFirstAddTime: '',
	startAddTime: '',
	endAddTime: '',
	startFirstHfTime: '',
	endFirstHfTime: '',
	startLastHfTime: '',
	endLastHfTime: '',
	sex: '',
	isConvert: '',
	guestType: '',
	status: '',
	customerSource: '1688展厅来访'
});
const guestTypeList = ref([
	{ label: '未知', value: 0 },
	{ label: '厂家', value: 1 },
	{ label: '商家', value: 2 },
	{ label: '厂家商家一体', value: 3 },
	{ label: '游客', value: 4 },
]);
const statusList = ref([
	{ label: '跟进', value: '跟进' },
	{ label: '丢失', value: '丢失' },
	{ label: '转化', value: '转化' },
]);
const fkForm = ref({});
const fkId = ref('');
const excelUrl = ref('');
const hfId = ref('');
const table = ref();
const isAdd = ref(false);
const visitorLog = (row: any) => {
	fkId.value = row.id;
	recordsVisible.value = true;
};

const returnVisityLog = (row: any) => {
	hfId.value = row.id;
	returnVisible.value = true;
};

const visitorRegistration = (type: boolean, row: any) => {
	isAdd.value = type;
	fkForm.value = row;
	addVisitorVisible.value = true;
};

const close = () => {
	addVisitorVisible.value = false;
	returnVisible.value = false;
	recordsVisible.value = false;
	table.value.refreshTable(true);
}

const previewFile = (attachmentFile: string) => {
	const res = JSON.parse(attachmentFile);
	drawer.value = true
	excelUrl.value = res[0].url
}

const downLoad = (attachmentFile: string) => {
	const res = JSON.parse(attachmentFile);
	downLoadFile(res[0].url, res[0].name);
}
const tableCols = ref<VxeTable.Columns[]>([
	{ width: 100, sortable: true, field: 'cjName', title: '公司名称' },
	{ width: 120, sortable: true, field: 'lastGuestTime', title: '到访时间', formatter: 'formatDate', align: 'center' },
	{
		width: 80,
		sortable: true,
		align: 'center',
		field: 'guestType',
		title: '访客类型',
		formatter: (row: any) => (row.guestType !== null ? guestTypeList.value.find((item: any) => item.value == row.guestType)?.label : row.guestType),
	},
	{ width: 100, field: 'mainBusinessCategories', title: '主营类目', sortable: true, },
	{ width: 150, sortable: true, field: 'intent', title: '访客意向描述' },
	{ width: 70, align: 'center', sortable: true, field: 'status', title: '状态', formatter: (row: any) => (row.status !== null ? statusList.value.find((item: any) => item.value == row.status)?.label : row.status) },
	{ width: 90, sortable: true, field: 'lostReason', title: '丢失原因' },
	{ width: 90, sortable: true, field: 'followUserName', title: '跟进人' },
	{ width: 80, align: 'center', sortable: true, field: 'realName', title: '访客姓名', },
	{ width: 120, sortable: true, field: 'contactPhone', title: '访客联系电话', },
	{ width: 120, sortable: true, field: 'cjAddr', title: '访客地址' },
	{ width: 105, sortable: true, field: 'jieDaiUserName', title: '接待员' },
	{ width: 90, sortable: true, field: 'guestTimes', title: '到访次数', type: 'click', handle: (row: any) => visitorLog(row), },
	{
		field: '20250611104955',
		title: '操作',
		type: 'btnList',
		align: 'center',
		btnList: [
			{ title: '编辑', handle: (row: any) => visitorRegistration(false, row) },
			{ title: '跟进记录', handle: (row: any) => returnVisityLog(row), formatter: (row: any) => `跟进记录${row.hfTimes ?? 0}条` },
			{ title: '查看附件', handle: (row: any) => previewFile(row.attachmentFile), isDisabled: (row: any) => !row.attachmentFile },
			{ title: '下载附件', handle: (row: any) => downLoad(row.attachmentFile), isDisabled: (row: any) => !row.attachmentFile },
		],
	},
]);
</script>

<style scoped lang="scss"></style>
