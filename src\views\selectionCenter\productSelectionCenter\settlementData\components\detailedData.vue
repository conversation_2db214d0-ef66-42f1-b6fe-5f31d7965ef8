<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange class="publicCss" v-model:startDate="query.billTimeStart" v-model:endDate="query.billTimeEnd" startPlaceholder="单据开始时间" endPlaceholder="单据结束时间" style="width: 220px" />
				<div class="publicCss">
					<manyInput v-model:inputt="query.supplierNames" :title="'供应商名称'" :verifyNumber="false" :placeholder="'请输入供应商名称'" :maxRows="100" :maxlength="1000" />
				</div>
				<el-select v-model="query.billTypes" placeholder="类型" class="publicCss_select" clearable filterable multiple collapse-tags collapse-tags-tooltip>
					<el-option v-for="item in typelist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.balanceStatus" placeholder="状态" class="publicCss_select" clearable filterable multiple collapse-tags collapse-tags-tooltip>
					<el-option v-for="item in statuslist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<div class="publicCss">
					<manyInput v-model:inputt="query.buyNos" :title="'采购单号'" :verifyNumber="false" :placeholder="'请输入采购单号'" :maxRows="100" :maxlength="1000" />
				</div>
				<dataRange
					class="publicCss"
					startPlaceholder="结算开始时间"
					endPlaceholder="结算结束时间"
					v-model:startDate="query.balanceTimeStart"
					v-model:endDate="query.balanceTimeEnd"
					style="width: 220px"
				/>
				<div class="publicCss">
					<manyInput v-model:inputt="query.balanceIds" :title="'结算单号'" :verifyNumber="false" :placeholder="'请输入结算单号'" :maxRows="100" :maxlength="1000" />
				</div>
				<div class="publicCss">
					<manyInput v-model:inputt="query.goodsCodes" :title="'商品编码'" :verifyNumber="false" :placeholder="'请输入商品编码'" :maxRows="100" :maxlength="1000" />
				</div>
				<div class="pb5">
					<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				id="202506241333"
				:tableCols="tableCols"
				showsummary
				isIndexFixed
				:query="query"
				:query-api="GetCwDelayPaySupplierInfoDtlList"
				:export-api="ExportCwDelayPaySupplierInfoDtl"
				:asyncExport="{
					title: `明细数据信息 ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`,
					isAsync: true,
				}"
			>
				<template #toolbar_buttons>
					<el-button @click="adjustmentMethod(1)" type="primary">新增调整</el-button>
					<el-button @click="adjustmentMethod(2)" type="primary">结算</el-button>
				</template>
				<template #wareCount="{ row }">
					<span :style="row.wareCount > 0 ? 'color: #e40a0a' : row.wareCount < 0 ? 'color: #46aa96' : ''">
						{{ row.wareCount?.toFixed(0) || '' }}
					</span>
				</template>
			</vxetable>
			<el-dialog v-model="adjustmentIfo.visible" width="30%" draggable overflow :title="isSettlement ? '结算' : '新增调整'" style="margin-top: -20vh !important">
				<div class="adjustment-form">
					<el-form :model="adjustmentIfo.form" ref="adjustmentFormRef" :rules="adjustmentRules" label-width="100px" class="adjustment-form-content">
						<el-form-item label="供应商" prop="supplierId">
							<el-select v-model="adjustmentIfo.form.supplierId" placeholder="请选择供应商" style="width: 100%" clearable filterable>
								<el-option v-for="item in props.supplierList" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>
						<el-form-item label="商品编码" prop="goodsCode">
							<el-input v-model.trim="adjustmentIfo.form.goodsCode" placeholder="请输入商品编码" style="width: 100%" clearable maxlength="50" />
						</el-form-item>
						<el-form-item label="调整日期" prop="adjustTime" v-if="!isSettlement">
							<el-date-picker v-model="adjustmentIfo.form.adjustTime" type="date" placeholder="请选择调整日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 100%" />
						</el-form-item>
						<el-form-item label="调整数量" prop="count" v-if="!isSettlement">
							<el-input-number
								:controls="false"
								v-model="adjustmentIfo.form.count"
								:max="999999"
								:min="-999999"
								controls-position="right"
								:precision="0"
								style="width: 100%"
								placeholder="+为增加，-为减少"
							/>
						</el-form-item>
						<el-form-item label="结算时间" prop="startTime" v-if="isSettlement">
							<dataRange
								class="publicCss"
								startPlaceholder="结算开始时间"
								endPlaceholder="结算结束时间"
								v-model:startDate="adjustmentIfo.form.startTime"
								v-model:endDate="adjustmentIfo.form.endTime"
								style="width: 100%"
							/>
						</el-form-item>
					</el-form>
					<div class="form-buttons">
						<el-button @click="cancelAdjustment">取消</el-button>
						<el-button type="primary" @click="confirmAdjustment">确定</el-button>
					</div>
				</div>
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, withDefaults, defineProps, nextTick, defineExpose } from 'vue';
import { GetCwDelayPaySupplierInfoDtlList, ExportCwDelayPaySupplierInfoDtl, CreateCwDelayPayAdjust, CreateCwDelayPayBalance } from '/@/api/cwManager/cwDelayPay';
import dayjs from 'dayjs';
import { ElMessage } from 'element-plus';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const manyInput = defineAsyncComponent(() => import('/@/components/yhCom/manyInput.vue'));
interface Supplier {
	value: string | number;
	label: string;
	[key: string]: any;
}

const props = withDefaults(
	defineProps<{
		isEncoding?: boolean;
		supplierList: Supplier[];
		encodedInfo: any;
	}>(),
	{
		isEncoding: false,
		supplierList: () => [],
		encodedInfo: () => {},
	}
);
const query = ref({
	billTimeStart: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
	billTimeEnd: dayjs().format('YYYY-MM-DD'),
	supplierNames: '', //供应商名称
	billTypes: [], //类型
	balanceStatus: [], //状态
	buyNos: '', //采购单号
	balanceTimeStart: '', //结算开始时间
	balanceTimeEnd: '', //结算结束时间
	balanceIds: '', //结算单号
	goodsCodes: '', //商品编码
});
const adjustmentIfo = ref({
	visible: false,
	form: {
		supplierId: '',
		supplierName: '',
		goodsCode: '',
		adjustTime: '',
		count: undefined,
		startTime: '',
		endTime: '',
	},
});
const isSettlement = ref(false);
const adjustmentFormRef = ref();
const adjustmentRules = ref({
	adjustTime: [{ required: true, message: '请选择调整日期' }],
	count: [{ required: true, message: '请输入调整数量' }],
	supplierId: [{ required: true, message: '请选择供应商' }],
	goodsCode: [{ required: true, message: '请输入商品编码' }],
	startTime: [{ required: true, message: '请选择结算时间' }],
	endTime: [{ required: true, message: '请选择结算时间' }],
});
const typelist = ref([
	{ label: '采购入库', value: '采购入库' },
	{ label: '手动调整', value: '手动调整' },
	{ label: '退货出库', value: '退货出库' },
	{ label: '销售出库', value: '销售出库' },
]);
const statuslist = ref([
	{ label: '无需结算', value: -1 },
	{ label: '待结算', value: 0 },
	{ label: '待审核', value: 1 },
	{ label: '已结算', value: 2 },
]);
const table = ref();

const parentMethod = (row?: any) => {
	const info = row || props.encodedInfo;
	query.value.supplierNames = props.isEncoding ? info.supplierName || '' : '';
	query.value.goodsCodes = props.isEncoding ? info.goodsCode || '' : '';
	table.value.refreshTable(true);
};

const adjustmentMethod = (type: number) => {
	adjustmentIfo.value.visible = true;
	nextTick(() => {
		adjustmentFormRef.value.resetFields();
		isSettlement.value = type == 1 ? false : true;
		adjustmentIfo.value.form = {
			supplierId: props.isEncoding ? props.encodedInfo.supplierId : '',
			goodsCode: props.isEncoding ? props.encodedInfo.goodsCode : '',
			adjustTime: dayjs().format('YYYY-MM-DD'),
			count: undefined,
			supplierName: '',
			startTime: '',
			endTime: '',
		};
	});
};

// 取消调整
const cancelAdjustment = () => {
	adjustmentIfo.value.visible = false;
	// 重置表单
	adjustmentIfo.value.form = {
		supplierId: '',
		goodsCode: '',
		adjustTime: '',
		count: undefined,
		supplierName: '',
		startTime: '',
		endTime: '',
	};
};

// 确定调整
const confirmAdjustment = async () => {
	adjustmentFormRef.value.validate(async (valid: boolean) => {
		if (valid) {
			// 提交成功后关闭弹窗
			let params = { ...adjustmentIfo.value.form, supplierName: props.supplierList.find((item) => item.value == adjustmentIfo.value.form.supplierId)?.label } as any;
			if (isSettlement.value) {
				params.startTime = dayjs(params.startTime).format('YYYY-MM-DD');
				params.endTime = dayjs(params.endTime).format('YYYY-MM-DD');
				delete params.supplierName;
				delete params.adjustTime;
				delete params.count;
			} else {
				delete params.startTime;
				delete params.endTime;
			}
			let api = isSettlement.value ? CreateCwDelayPayBalance : CreateCwDelayPayAdjust;
			const { success } = await api(params);
			if (success) {
				ElMessage.success('操作成功');
				adjustmentIfo.value.visible = false;
				table.value.refreshTable(true);
				// 重置表单
				adjustmentIfo.value.form = {
					supplierId: '',
					goodsCode: '',
					adjustTime: '',
					count: undefined,
					supplierName: '',
					startTime: '',
					endTime: '',
				};
			}
		}
	});
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'supplierName', title: '供应商名称', width: '250', align: 'center' },
	{ sortable: true, field: 'billTime', title: '时间', width: '135' },
	{ sortable: true, field: 'billType', title: '类型', align: 'center' },
	{ sortable: true, field: 'goodsCode', title: '商品编码', align: 'center', width: '230' },
	{ sortable: true, field: 'wareCount', title: '数量', align: 'right' },
	{ sortable: true, field: 'balanceStatus', title: '状态', align: 'center', formatter: (row: any) => row.balanceStatusName || '' },
	{ sortable: true, field: 'billId', title: '相关单据号', width: '190', align: 'center' },
	{ sortable: true, field: 'buyNo', title: '对应采购单号', align: 'center' },
	{ sortable: true, field: 'balanceTime', title: '结算时间', align: 'center', width: '135' },
	{ sortable: true, field: 'balanceCount', title: '结算数量', formatter: 'fmtAmt0', align: 'right' },
	{ sortable: true, field: 'balanceId', title: '结算单号', align: 'center' },
]);

defineExpose({
	parentMethod,
});
</script>

<style scoped lang="scss">
.adjustment-form {
	padding: 20px;
	background-color: #f8f9fa;
	border-radius: 8px;

	.adjustment-form-content {
		background-color: white;
		padding: 30px;
		border-radius: 8px;
		margin-bottom: 20px;

		:deep(.el-form-item) {
			margin-bottom: 25px;

			.el-form-item__label {
				font-weight: 500;
				color: #333;
			}

			.el-input__wrapper {
				border-radius: 4px;
				border: 1px solid #dcdfe6;

				&:hover {
					border-color: #c0c4cc;
				}

				&.is-focus {
					border-color: #409eff;
				}
			}

			.el-date-editor {
				width: 100%;
			}
		}
	}

	.form-buttons {
		display: flex;
		justify-content: center;
		gap: 20px;

		.el-button {
			min-width: 80px;
			height: 36px;
			border-radius: 4px;
			font-weight: 500;
		}
	}
}

// 弹窗标题样式
:deep(.el-dialog__header) {
	padding: 20px 20px 10px 20px;

	.el-dialog__title {
		font-size: 16px;
		font-weight: 600;
		color: #333;
	}
}

:deep(.el-dialog__body) {
	padding: 10px 20px 20px 20px;
}

.publicCss_select {
	width: 160px;
	margin: 0 10px 5px 0;
}
</style>
