import { VxeColumnPropTypes } from 'vxe-table';
import dayjs from 'dayjs';
import { platformlist, platformLink } from './tools';
type valType = string | number | boolean | null | undefined;
//时间格式化
const formatTime: VxeColumnPropTypes.Formatter<valType> = (val: any): string => {
	return val !== null ? dayjs(val).format('YYYY-MM-DD HH:mm:ss') : val;
};

//日期格式化
const formatDate: VxeColumnPropTypes.Formatter<valType> = (val: any): string => {
	return val !== null ? dayjs(val).format('YYYY-MM-DD') : val;
};

//布尔值格式化
const formatBoolean: VxeColumnPropTypes.Formatter<valType> = (val: any): string => {
	return val ? '是' : '否';
};

//平台格式化
const formatPlatform: VxeColumnPropTypes.Formatter<valType> = (val: any): string => {
	return platformlist.find((item) => item.value === val)?.label || val;
};

//给宝贝ID添加连接
const formatLinkProcode: any = (procode: any, platform: any) => {
	if (procode && platform) {
		const link = platformLink[platform];
		return `<a href="${link}${procode}" target="_blank" style="color: #1000ff;">${procode}</a>`;
	}
	return procode;
};

const fmtAmt2: VxeColumnPropTypes.Formatter<valType> = (val: any): string => {
	if (val === null || val === undefined) return val;
	const valAbs = Math.abs(val);
	if (valAbs >= 1000) {
		return val.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
	} else {
		return val !== null && val !== undefined ? val.toFixed(2) : val;
	}
};
const fmtAmt4: VxeColumnPropTypes.Formatter<valType> = (val: any): string => {
	if (val === null || val === undefined) return val;
	const valAbs = Math.abs(val);
	if (valAbs >= 1000) {
		return val.toLocaleString('en-US', { minimumFractionDigits: 4, maximumFractionDigits: 4 });
	} else {
		return val !== null && val !== undefined ? val.toFixed(4) : val;
	}
};
const fmtAmt0: VxeColumnPropTypes.Formatter<valType> = (val: any): string => {
	if (val === null || val === undefined) return val;
	const valAbs = Math.abs(val);
	if (valAbs >= 1000) {
		return val.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 });
	} else {
		return val !== null && val !== undefined ? val.toFixed(0) : val;
	}
};

// 百分比格式化
const fmtPercent: VxeColumnPropTypes.Formatter<valType> = (val: any): string => {
	return val == 0 ? '0.00%' : val !== null && val !== undefined ? `${val}%` : val;
};

//大于100的数字四舍五入不需要小数点,大于以前的保留千分位,不需要小数点,小于100的不变
const fmtNum: VxeColumnPropTypes.Formatter<valType> = (val: any): string => {
	if (val === null || val === undefined) return val;
	const valAbs = Math.abs(val);
	if (valAbs >= 100) {
		return val.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 });
	} else {
		return val !== null && val !== undefined ? val : val;
	}
};

export const formatters: Formatters.formatters<valType> = {
	formatTime,
	formatDate,
	formatBoolean,
	formatPlatform,
	formatLinkProcode,
	fmtAmt0,
	fmtAmt2,
	fmtAmt4,
	fmtPercent,
	fmtNum,
};
