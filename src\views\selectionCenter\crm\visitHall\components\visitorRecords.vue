<template>
	<div>
		<Container style="height: 65%;">
			<template #header>
				<div class="topCss">
					<dataRange class="publicCss" v-model:startDate="query.startGuestTime"
						v-model:endDate="query.endGuestTime" style="width: 230px" start-placeholder="到访时间"
						end-placeholder="到访时间" />
					<dataRange class="publicCss" v-model:startDate="query.startAddTime"
						v-model:endDate="query.endAddTime" style="width: 230px" start-placeholder="登记开始时间"
						end-placeholder="登记结束时间" />
					<div class="pb5">
						<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
					</div>
				</div>
			</template>
			<template #content>
				<vxetable ref="table" id="20250611135817" orderBy="addTime" :is-asc="false" :tableCols="tableCols"
					showsummary isIndexFixed :query="query" :query-api="GetCrmGuestRecordList"
					:asyncExport="{ title: '现金流', isAsync: false }">
				</vxetable>
			</template>
		</Container>
		<el-form ref="ruleFormRef" style="width: 100%; margin-top: 10px;" :model="ruleForm" status-icon :rules="rules"
			label-width="auto" class="demo-ruleForm">
			<el-form-item label="到访时间" prop="guestTime">
				<el-date-picker v-model="ruleForm.guestTime" class="w20" type="datetime" placeholder="到访时间"
					value-format="YYYY-MM-DD HH:mm:ss" :clearable="false" />
			</el-form-item>
			<el-form-item label="来访意向" prop="guestIntent">
				<el-input type="textarea" row="3" maxlength="4000" v-model="ruleForm.guestIntent" autocomplete="off"
					resize="none" />
			</el-form-item>
			<el-form-item label="接待人备注" prop="jdRemark">
				<el-input type="textarea" row="3" maxlength="4000" v-model.number="ruleForm.jdRemark" resize="none" />
			</el-form-item>
			<el-form-item>
				<div class="mt10 w100" style="display: flex; justify-content: center">
					<el-button @click="emit('close')">关闭</el-button>
					<el-button type="primary" @click="submitForm(ruleFormRef)" v-reclick="2000"> 提交 </el-button>
				</div>
			</el-form-item>
		</el-form>
	</div>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, reactive } from 'vue';
import { GetCrmGuestRecordList, AddorEditCrmGuestRecord } from '/@/api/customerservice/customerGroup';
import type { FormInstance, FormRules } from 'element-plus';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
const props = defineProps({
	fkId: {
		type: String,
		default: '',
	},
});
const query = ref({
	fkId: props.fkId,
	startGuestTime: '',
	endGuestTime: '',
	startAddTime: '',
	endAddTime: '',
});
const ruleFormRef = ref<FormInstance>();
const emit = defineEmits(['close', 'getList']);
const table = ref();
const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'guestTime', title: '到访时间', formatter: 'formatDate' },
	{ sortable: true, field: 'guestIntent', title: '来访意向' },
	{ sortable: true, field: 'jdRemark', title: '接待人备注' },
	{ sortable: true, field: 'jdUserName', title: '接待人姓名' },
	{ sortable: true, field: 'addTime', title: '登记时间', formatter: 'formatDate' },
]);
const ruleForm = ref({
	guestTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
	guestIntent: '',
	jdRemark: '',
	guestId: props.fkId,
});

const rules = reactive<FormRules<typeof ruleForm>>({
	guestTime: [{ required: true, message: '请选择到访时间', trigger: 'change' }],
});

const submitForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	formEl.validate(async (valid) => {
		if (valid) {
			const { success } = await AddorEditCrmGuestRecord(ruleForm.value);
			if (success) {
				table.value.refreshTable(true);
				ruleForm.value = {
					guestTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
					guestIntent: '',
					jdRemark: '',
					guestId: props.fkId,
				};
				window.$message({
					message: '新增成功',
					type: 'success',
				});
			} else {
				console.error('Error submitting form:', success);
			}
		} else {
			console.log('error submit!');
		}
	});
};
</script>

<style scoped lang="scss"></style>
