<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" style="height: 100%; width: 100%" @tab-click="handleTabClick">
				<el-tab-pane label="编码维度" name="first" style="height: 100%">
					<encodingDimension ref="refEncodingDimension" @handleQuery="handleQuery" />
				</el-tab-pane>
				<el-tab-pane label="采购单维度" name="second" style="height: 100%" lazy>
					<purchaseOrderDimension ref="refPurchaseOrderDimension" />
				</el-tab-pane>
				<el-tab-pane label="明细数据" name="third" style="height: 100%">
					<detailedData ref="refDetailedData" :isEncoding="isEncoding" :supplierList="supplierList" :encodedInfo="encodedInfo" />
				</el-tab-pane>
				<el-tab-pane label="结算单" name="fourth" style="height: 100%" lazy>
					<settlementBill ref="refSettlementBill" />
				</el-tab-pane>
			</el-tabs>
		</template>
	</Container>
</template>

<script lang="ts" setup>
import { GetCwDelayPaySupplier } from '/@/api/cwManager/cwDelayPay';
import { ref, defineAsyncComponent, onMounted, nextTick } from 'vue';
const encodingDimension = defineAsyncComponent(() => import('./components/encodingDimension.vue'));
const purchaseOrderDimension = defineAsyncComponent(() => import('./components/purchaseOrderDimension.vue'));
const detailedData = defineAsyncComponent(() => import('./components/detailedData.vue'));
const settlementBill = defineAsyncComponent(() => import('./components/settlementBill.vue'));
const activeName = ref('first');
const refDetailedData = ref<InstanceType<typeof detailedData>>();
const refEncodingDimension = ref<InstanceType<typeof encodingDimension>>();
const isEncoding = ref(false);
const supplierList = ref([]);
const encodedInfo = ref({});
const handleQuery = (row: any) => {
	// 先更新编码信息
	isEncoding.value = true;
	encodedInfo.value = { ...row };
	// 等 prop 更新完再调用子组件方法，并把数据直接传过去
	setTimeout(() => {
		refDetailedData.value?.parentMethod(row);
		activeName.value = 'third';
	}, 100);
};

const handleTabClick = (tab: any) => {
	activeName.value = tab.props.name;
	if (tab.props.name === 'third') {
		isEncoding.value = false;
		encodedInfo.value = {};
	}
};

onMounted(async () => {
	const { data } = await GetCwDelayPaySupplier({});
	supplierList.value = data.map((i: any) => ({
		value: i.supplierId,
		label: i.supplierName,
	}));
});
</script>
