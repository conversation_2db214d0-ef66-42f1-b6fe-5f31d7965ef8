<template>
    <div>
        <Container style="height: 60%">
            <template #header>
                <div class="topCss">
                    <dataRange class="publicCss" v-model:startDate="query.startHfTime" v-model:endDate="query.endHfTime"
                        style="width: 230px" start-placeholder="跟进时间" end-placeholder="跟进时间" />
                    <dataRange class="publicCss" v-model:startDate="query.startAddTime"
                        v-model:endDate="query.endAddTime" style="width: 230px" start-placeholder="登记开始时间"
                        end-placeholder="登记结束时间" />
                    <div class="pb5">
                        <el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
                    </div>
                </div>
            </template>
            <template #content>
                <vxetable ref="table" orderBy="addTime" :is-asc="false" id="20250510133901" :tableCols="tableCols"
                    showsummary isIndexFixed :query="query" :query-api="GetCrmGuestHfRecordList"> </vxetable>
            </template>
        </Container>
        <el-form ref="ruleFormRef" style="width: 100%; margin-top: 10px;" :model="ruleForm" status-icon :rules="rules"
            label-width="auto" class="demo-ruleForm">
            <el-form-item label="跟进时间" prop="hfTime">
                <el-date-picker v-model="ruleForm.hfTime" style="width: 200px;" type="datetime" placeholder="跟进时间"
                    value-format="YYYY-MM-DD HH:mm:ss" :clearable="false" />
            </el-form-item>
            <el-form-item label="跟进状态">
                <el-select v-model="ruleForm.hfStatus" placeholder="跟进状态" style="width: 200px;" class="publicCss"
                    @change="changeStatus">
                    <el-option label="跟进" value="跟进" />
                    <el-option label="丢失" value="丢失" />
                    <el-option label="已拉群" value="已拉群" />
                    <el-option label="已入仓" value="已入仓" />
                </el-select>
            </el-form-item>
            <el-form-item label="丢失原因" v-if="ruleForm.hfStatus == '丢失'" prop="lostReason">
                <el-input type="textarea" row="3" maxlength="200" placeholder="丢失原因" v-model="ruleForm.lostReason"
                    resize="none" />
            </el-form-item>
            <el-form-item label="跟进意向">
                <el-input type="textarea" row="3" maxlength="4000" placeholder="跟进意向" v-model="ruleForm.hfIntent"
                    resize="none" autocomplete="off" />
            </el-form-item>
            <el-form-item label="跟进人备注">
                <el-input type="textarea" row="3" maxlength="4000" placeholder="跟进人备注" v-model="ruleForm.hfRemark"
                    resize="none" />
            </el-form-item>
            <el-form-item label="业务员">
                <userSelect style="width: 220px" v-model:value="ruleForm.hfUserId" :isDdUserId="false"
                    v-model:label="ruleForm.hfUserName" placeholder="请输入业务员姓名" :clearable="false"
                    v-if="ruleForm.hfUserId" />
            </el-form-item>
            <el-form-item>
                <div class="mt10 w100" style="display: flex; justify-content: center">
                    <el-button @click="emit('close')">取消</el-button>
                    <el-button type="primary" @click="submitForm(ruleFormRef)" v-reclick="2000"> 提交 </el-button>
                </div>
            </el-form-item>
        </el-form>
    </div>

</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, reactive, onMounted } from 'vue';
import dayjs from 'dayjs';
import { GetCrmGuestHfRecordList, AddorEditCrmGuestHfRecord } from '/@/api/customerservice/customerGroup';
import type { FormInstance, FormRules } from 'element-plus';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
const userSelect = defineAsyncComponent(() => import('/@/components/yhCom/userSelect.vue'));
import { getUserInfo } from "/@/api/operatemanage/productalllink";
const emit = defineEmits(['close', 'getList']);
const props = defineProps({
    hfId: {
        type: String,
        default: '',
    },
});
const userInfo = ref<any>({})
const rules = reactive<FormRules<typeof ruleForm>>({
    hfTime: [{ required: true, message: '请选择跟进时间', trigger: 'change' }],
    lostReason: [{ required: true, message: '请输入丢失原因', trigger: 'blur' }],
});
const ruleFormRef = ref<FormInstance>();
const ruleForm = ref<{
    hfTime: string;
    hfIntent: string;
    hfRemark: string;
    guestId: string;
    hfStatus: string;
    lostReason: string;
    hfUserId: number | null;
    hfUserName: string;
}>({
    hfTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    hfIntent: '',
    hfRemark: '',
    guestId: props.hfId,
    hfStatus: '',
    lostReason: '',
    hfUserId: null,
    hfUserName: '',
});

const query = ref({
    hfId: props.hfId,
    startHfTime: '',
    endHfTime: '',
    startAddTime: '',
    endAddTime: '',
});
const table = ref();
const tableCols = ref<VxeTable.Columns[]>([
    { sortable: true, field: 'hfTime', title: '跟进时间', formatter: 'formatDate' },
    { sortable: true, field: 'hfIntent', title: '跟进意向' },
    { sortable: true, field: 'hfRemark', title: '跟进人备注' },
    { sortable: true, field: 'hfStatus', title: '跟进状态' },
    { sortable: true, field: 'hfUserName', title: '跟进人姓名' },
    { sortable: true, field: 'addTime', title: '登记时间', formatter: 'formatDate' },
]);
const changeStatus = (val: string) => {
    if (val != '丢失') {
        ruleForm.value.lostReason = '';
    }
};

const submitForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    formEl.validate(async (valid) => {
        if (valid) {
            const { success } = await AddorEditCrmGuestHfRecord(ruleForm.value);
            if (success) {
                table.value.refreshTable(true);
                window.$message({
                    message: '新增跟进记录成功',
                    type: 'success',
                });
                ruleForm.value = {
                    hfTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                    hfIntent: '',
                    hfRemark: '',
                    guestId: props.hfId,
                    hfStatus: '',
                    lostReason: '',
                    hfUserId: Number(userInfo.value?.id),
                    hfUserName: userInfo.value?.nickName,
                };
            }
        } else {
            console.log('error submit!');
        }
    });
};

onMounted(async () => {
    const { data } = await getUserInfo();
    userInfo.value = data
    ruleForm.value.hfUserId = Number(data.id) ?? null
    ruleForm.value.hfUserName = data.nickName
})
</script>

<style scoped lang="scss"></style>
