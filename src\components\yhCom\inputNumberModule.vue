<template>
	<el-input
		v-model="innerValue"
		:placeholder="placeholder"
		:clearable="clearable"
		:maxlength="maxlength"
		:disabled="disabled"
		@input="handleAmountInput"
		@blur="handleAmountBlur"
		@clear="handleAmountClear"
		:style="cststyle"
	/>
</template>

<script lang="ts">
import { defineComponent, PropType, defineEmits, defineProps, ref, watch, onMounted } from 'vue';

export default defineComponent({
	props: {
		// 双向绑定值
		modelValue: {
			type: [String, Number, null] as PropType<string | number | null>,
			default: null,
		},
		// 输入框占位符
		placeholder: {
			type: String,
			default: '请输入数值',
		},
		// 是否显示清空按钮
		clearable: {
			type: Boolean,
			default: true,
		},
		// 是否禁用
		disabled: {
			type: Boolean,
			default: false,
		},
		// 自定义样式
		cststyle: {
			type: Object as PropType<Record<string, string>>,
			default: () => ({}),
		},
		// 最小值
		min: {
			type: Number,
			default: 0,
		},
		// 最大值
		max: {
			type: Number,
			default: 9999999,
		},
		// 最大长度
		maxlength: {
			type: Number,
			default: 50,
		},
		// 小数位数
		fixed: {
			type: Number,
			default: 0,
			validator(value: number) {
				return value >= 0;
			},
		},
		// 是否显示千分位
		thousands: {
			type: Boolean,
			default: false, // 默认为不显示千分位
		},
		inputDefault: {
			type: Boolean,
			default: false,
		},
	},
	setup(props, { emit }) {
		onMounted(() => {
			if (props.modelValue) {
				innerValue.value = formatNumber(props.modelValue);
			}
		});
		const innerValue = ref<string | null>(props.modelValue ? String(props.modelValue) : null);

		const isValidNumber = (val: any): boolean => {
			if (typeof val === 'number' && !isNaN(val)) {
				return true;
			}
			if (typeof val === 'string') {
				const parsed = parseFloat(val);
				return !isNaN(parsed);
			}
			return false;
		};

		const formatNumber = (value: number | string): string => {
			if (typeof value === 'number' && props.thousands) {
				return value.toLocaleString('en-US', {
					minimumFractionDigits: props.fixed,
					maximumFractionDigits: props.fixed,
				});
			}
			return String(value);
		};

		const handleAmountClear = () => {
			emit('update:modelValue', null);
		};

		const handleAmountInput = (value: string) => {
			let regex: RegExp | null = null; // 初始化为 null
			let filteredValue: string;
			if (!value) {
				emit('clearingMethod');
			}
			if (props.inputDefault) {
				// 允许前导零的情况下，不限制正则，直接保留输入
				filteredValue = value;
			} else if (props.min < 0) {
				// 禁止前导零，负数支持
				regex = props.fixed > 0 ? new RegExp(`^-?\\d*(\\.\\d{0,${props.fixed}})?`) : new RegExp('^-?\\d*');
				filteredValue = value.replace(/[^0-9.-]/g, ''); // 仅保留数字、负号和小数点
			} else {
				// 禁止前导零，正数支持
				regex = props.fixed > 0 ? new RegExp(`^\\d*(\\.\\d{0,${props.fixed}})?`) : new RegExp('^\\d*');
				filteredValue = value.replace(/[^0-9.]/g, ''); // 仅保留数字和小数点
			}
			// 如果 regex 存在，则进行正则匹配
			const validValue = regex ? filteredValue.match(regex)?.[0] || '' : filteredValue;
			const numericValue = parseFloat(validValue);

			if (isNaN(numericValue) || numericValue < props.min || numericValue > props.max) {
				innerValue.value = validValue;
			} else {
				// 更新模型值
				if (props.inputDefault) {
					emit('update:modelValue', filteredValue); // 前导零时直接传递字符串
				} else {
					emit('update:modelValue', numericValue); // 无前导零时传递数值
				}
				innerValue.value = validValue;
			}
		};

		const handleAmountBlur = () => {
			if (!innerValue.value) {
				emit('update:modelValue', null);
				innerValue.value = '';
				return;
			}
			if (!props.inputDefault) {
				innerValue.value = innerValue.value.replace(/^0+(?!\.|$)/, '');
			} else {
				const isLeadingZero = innerValue.value.startsWith('0') && !innerValue.value.includes('.');
				if (isLeadingZero) {
					emit('update:modelValue', innerValue.value); // 保留前导零的原始值
					return;
				}
			}
			const numericValue = parseFloat(innerValue.value);
			if (isNaN(numericValue)) {
				emit('update:modelValue', null);
				innerValue.value = '';
			} else if (numericValue < props.min) {
				emit('update:modelValue', props.min);
				innerValue.value = formatNumber(props.min);
			} else if (numericValue > props.max) {
				emit('update:modelValue', props.max);
				innerValue.value = formatNumber(props.max);
			} else {
				emit('update:modelValue', numericValue);
				innerValue.value = props.fixed > 0 ? numericValue.toFixed(props.fixed) : String(Math.floor(numericValue));
			}
			emit('valueMethod', innerValue.value);
		};

		watch(
			() => props.modelValue,
			(newValue) => {
				innerValue.value = newValue ? String(newValue) : null;
			}
		);

		return {
			innerValue,
			handleAmountInput,
			handleAmountBlur,
			handleAmountClear,
		};
	},
});
</script>
