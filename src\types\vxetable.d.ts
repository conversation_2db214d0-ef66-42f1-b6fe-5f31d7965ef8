declare namespace VxeTable {
	type typeList = 'click' | 'image' | 'btnList' | 'html' | 'switch';
	type VxeColumnPropTypes = 'formatTime' | 'formatDate' | 'formatBoolean' | 'formatPlatform' | 'formatLinkProcode' | 'fmtAmt0' | 'fmtAmt2' | 'fmtAmt4' | 'fmtPercent' | 'fmtNum';
	type btnList = {
		title?: string;
		handle?: (row: any) => void;
		isDisabled?: (row: any) => boolean | boolean;
		permissions?: string[] | string;
		formatter?: VxeColumnPropTypes | Function; // 格式化函数
	};
	//列配置
	interface Columns {
		sortable?: boolean; // 是否排序
		field?: string; // 字段名
		title?: string; // 标题
		align?: 'left' | 'center' | 'right'; // 对齐方式
		colType?: typeList; // 额外类型
		slots?: { default: typeList }; // 插槽
		handle?: (row: any) => void; // 点击事件
		width?: string | number; // 宽度
		btnList?: btnList[]; // 按钮列表
		isDisabled?: (row: any) => boolean | boolean; // 是否禁用
		type?: typeList; // 配置类型
		visible?: boolean; // 是否显示
		fixed?: 'left' | 'right'; // 固定列
		formatter?: VxeColumnPropTypes | Function; // 格式化函数
		showOverflow?: 'title' | 'ellipsis' | 'tooltip' | 'clip'; // 显示方式
		summaryEvent?: boolean; // 合计事件
		treeNode?: boolean; // 是否树节点
		color?: string | Function;
		component?: any;
		tipmesg?: string; //提示信息
		direction?: 'column' | 'row'; //btnList的排列方向，默认row
		minWidth?: number | string; //最小宽度
		children?: Columns[]; // 子列
		permissions?: string[] | string; // 权限
		copy?: boolean; // 是否复制
	}
	//右键菜单
	interface menuConfig {
		header: {
			options: [menuOptions[]];
		};
		body: {
			options?: Array;
		};
	}

	interface exportConfig {
		title?: string; // 导出类型
		isAsync?: boolean; // 是否异步导出
	}

	interface menuOptions {
		code?: string;
		name?: string;
		disabled?: boolean;
	}
}
