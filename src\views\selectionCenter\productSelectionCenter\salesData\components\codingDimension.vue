<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange class="publicCss" v-model:startDate="query.startDate" v-model:endDate="query.endDate" style="width: 200px" :clearable="false" />
				<div class="pb5">
					<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				id="202506091055"
				:tableCols="tableCols"
				showsummary
				isIndexFixed
				:query="query"
				:query-api="GetSelectionProductCenterSaleDataPlanA"
				@footerCellClick="onSummaryTotalMap"
				:export-api="ExportSelectionProductCenterSaleDataPlanA"
				:asyncExport="{
					title: `选品中心销售数据信息 ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`,
					isAsync: false,
				}"
			></vxetable>
			<el-dialog v-model="codingInfo.visible" title="款式编码详情" width="90%" draggable overflow>
				<codingDetails
					v-if="codingInfo.visible"
					:options="{
						isType: codingInfo.isType,
						rowData: codingInfo.rowData,
						fieldName: codingInfo.fieldName,
						isSummary: codingInfo.isSummary,
						parentData: query,
						operationsTeamList,
					}"
				/>
			</el-dialog>
			<el-dialog v-model="totalMapVisible" width="60%" draggable overflow>
				<div>
					<dataRange
						v-model:startDate="trendChart.startDate"
						v-model:endDate="trendChart.endDate"
						:clearable="false"
						startPlaceholder="开始时间"
						endPlaceholder="结束时间"
						style="width: 260px"
						@change="onTrendMethod({})"
					/>
					<lineChart
						v-if="totalMapVisible"
						:chartData="analysisData"
						ref="sumChart"
						:thisStyle="{
							width: '100%',
							height: '600px',
							'box-sizing': 'border-box',
							'line-height': '600px',
						}"
						:tooltipFormatter="customTooltipFormatter"
					/>
				</div>
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted, nextTick } from 'vue';
import dayjs from 'dayjs';
const lineChart = defineAsyncComponent(() => import('/@/components/yhCom/lineChart.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
import {
	GetSelectionProductCenterSaleDataPlanA,
	QuerySelectionProductCenterSaleDataPlanAAnalysis,
	ExportSelectionProductCenterSaleDataPlanA,
} from '/@/api/bookkeeper/selectionProductCenterSaleDataPlanA';
import { GetDirectorGroupList } from '/@/api/operatemanage/shopmanager';
import codingDetails from './codingDetails.vue';
const operationsTeamList = ref([]);
const query = ref({
	startDate: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
	endDate: dayjs().format('YYYY-MM-DD'),
	dataType: 1, //编码维度
});
const table = ref();
const codingInfo = ref({
	visible: false,
	isType: '',
	rowData: {},
	fieldName: '',
	isSummary: false,
});
const totalMapVisible = ref(false);
const trendChart = ref({
	startDate: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
	endDate: dayjs().format('YYYY-MM-DD'),
	modeType: '',
});
const analysisData = ref([]);
const sumChart = ref();

const customTooltipFormatter = (params: any) => {
	let result = params[0].name + '<br/>';
	params.forEach((param: any) => {
		const seriesName = param.seriesName;
		const value = param.value;
		const color = param.color;
		const percentageFields = ['毛六利润率'];
		const displayValue = percentageFields.includes(seriesName) ? `${value}%` : value;
		result += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
		result += `${seriesName}: ${displayValue}<br/>`;
	});
	return result;
};

const onDetailsMethod = (row: any, type: string, field: string) => {
	codingInfo.value.visible = true;
	codingInfo.value.isType = type;
	codingInfo.value.rowData = row;
	codingInfo.value.fieldName = field;
	codingInfo.value.isSummary = false;
};

const onSummaryTotalMap = async (row: any, column: any) => {
	codingInfo.value.visible = true;
	codingInfo.value.rowData = {};
	codingInfo.value.fieldName = column;
	codingInfo.value.isSummary = true;
	const codingFields = new Set(['styleCodeCount', 'goodsCodeCount', 'onLineIdCount']);
	const idFields = new Set(['orderCount', 'profit6', 'profit6Rate', 'saleAmount', 'goodsCodeOrderCount', 'goodsCount', 'exitCost', 'warehouseSalary']);
	if (codingFields.has(column)) {
		codingInfo.value.isType = 'Coding';
	} else if (idFields.has(column)) {
		codingInfo.value.isType = 'ID';
	}
};

const onTrendMethod = async (row: any) => {
	if (row && row.yearMonthDayDate) {
		trendChart.value.endDate = dayjs(row.yearMonthDayDate).format('YYYY-MM-DD');
		trendChart.value.startDate = dayjs(row.yearMonthDayDate).subtract(1, 'month').format('YYYY-MM-DD');
		trendChart.value.modeType = row.modeType;
	}

	const params = {
		startDate: trendChart.value.startDate,
		endDate: trendChart.value.endDate,
		modeType: trendChart.value.modeType,
		dataType: query.value.dataType, //编码维度
	};

	const { data, success } = await QuerySelectionProductCenterSaleDataPlanAAnalysis(params);
	if (success) {
		analysisData.value = data;
		totalMapVisible.value = true;
		nextTick(() => {
			if (sumChart.value) {
				sumChart.value.reSetChart(data);
			}
		});
	}
};

const defaultFormatter = (field: string) => (row: any) => {
	// 添加千分位格式化函数
	const formatNumber = (num: number) => {
		return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
	};
	if (field == 'profit6Rate') {
		return row[field] ? (row[field] * 100).toFixed(2) + '%' : '0%';
	}
	// 金额类字段，需要千分位格式化
	const amountFields = ['saleAmount', 'profit6', 'exitCost', 'warehouseSalary'];
	if (amountFields.includes(field)) {
		return row[field] ? formatNumber(parseFloat(row[field])) : '0.00';
	}
	// 数量类字段，整数千分位格式化
	const countFields = ['styleCodeCount', 'goodsCodeCount', 'onLineIdCount', 'orderCount', 'goodsCodeOrderCount'];
	if (countFields.includes(field)) {
		return row[field] ? parseInt(row[field]).toLocaleString('zh-CN') : '0';
	}
	return row[field] ? row[field] : '0';
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'yearMonthDayDate', title: '日期', formatter: 'formatDate' },
	{ sortable: true, field: 'modeType', title: '模式', align: 'center' },
	{
		summaryEvent: true,
		sortable: true,
		field: 'styleCodeCount',
		title: '款式编码数',
		align: 'right',
		formatter: defaultFormatter('styleCodeCount'),
		type: 'click',
		handle: (row: any) => onDetailsMethod(row, 'Coding', 'styleCodeCount'),
	},
	{
		summaryEvent: true,
		sortable: true,
		field: 'goodsCodeCount',
		title: '商品编码数',
		align: 'right',
		formatter: defaultFormatter('goodsCodeCount'),
		type: 'click',
		handle: (row: any) => onDetailsMethod(row, 'Coding', 'goodsCodeCount'),
	},
	{
		summaryEvent: true,
		sortable: true,
		field: 'onLineIdCount',
		title: '上架ID数',
		align: 'right',
		formatter: defaultFormatter('onLineIdCount'),
		type: 'click',
		handle: (row: any) => onDetailsMethod(row, 'Coding', 'onLineIdCount'),
	},
	{
		summaryEvent: true,
		sortable: true,
		field: 'orderCount',
		title: '订单量',
		align: 'right',
		formatter: defaultFormatter('orderCount'),
		type: 'click',
		handle: (row: any) => onDetailsMethod(row, 'ID', 'orderCount'),
	},
	{
		summaryEvent: true,
		sortable: true,
		field: 'profit6',
		title: '毛六利润(发生)',
		align: 'right',
		formatter: defaultFormatter('profit6'),
		type: 'click',
		handle: (row: any) => onDetailsMethod(row, 'ID', 'profit6'),
	},
	{
		summaryEvent: true,
		sortable: true,
		field: 'profit6Rate',
		title: '毛六利润率(发生)',
		align: 'right',
		formatter: defaultFormatter('profit6Rate'),
		type: 'click',
		handle: (row: any) => onDetailsMethod(row, 'ID', 'profit6Rate'),
	},
	{
		summaryEvent: true,
		sortable: true,
		field: 'saleAmount',
		title: '销售金额',
		align: 'right',
		formatter: defaultFormatter('saleAmount'),
		type: 'click',
		handle: (row: any) => onDetailsMethod(row, 'ID', 'saleAmount'),
	},
	{
		summaryEvent: true,
		sortable: true,
		field: 'goodsCodeOrderCount',
		title: '编码销售数',
		align: 'right',
		formatter: defaultFormatter('goodsCodeOrderCount'),
		type: 'click',
		handle: (row: any) => onDetailsMethod(row, 'ID', 'goodsCodeOrderCount'),
	},
	// {
	// 	summaryEvent: true,
	// 	sortable: true,
	// 	field: 'goodsCount',
	// 	title: '编码销量',
	// 	align: 'center',
	// 	formatter: defaultFormatter('goodsCount'),
	// 	type: 'click',
	// 	handle: (row: any) => onDetailsMethod(row, 'ID', 'goodsCount'),
	// },
	{
		summaryEvent: true,
		sortable: true,
		field: 'exitCost',
		title: '出库成本',
		align: 'right',
		formatter: defaultFormatter('exitCost'),
		type: 'click',
		handle: (row: any) => onDetailsMethod(row, 'ID', 'exitCost'),
	},
	{
		summaryEvent: true,
		sortable: true,
		field: 'warehouseSalary',
		title: '仓库薪资',
		align: 'right',
		formatter: defaultFormatter('warehouseSalary'),
		type: 'click',
		handle: (row: any) => onDetailsMethod(row, 'ID', 'warehouseSalary'),
	},
	{
		align: 'center',
		width: '100',
		type: 'btnList',
		field: 'action',
		minWidth: '100',
		fixed: 'right',
		btnList: [{ title: '趋势图', handle: (row) => onTrendMethod(row) }],
	},
]);
onMounted(() => {
	GetDirectorGroupList({}).then((res) => {
		operationsTeamList.value = res.data || [];
	});
});
</script>

<style scoped lang="scss"></style>
