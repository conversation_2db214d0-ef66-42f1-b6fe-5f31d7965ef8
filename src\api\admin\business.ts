import request from '/@/utils/yhrequest';
import judgePort from '/@/utils/judgePort';
const apiPrefix = `${judgePort() +import.meta.env.VITE_APP_BASE_API}/business/`;
export const GetVxeTableColumnCacheAsync = (params: any, config = {}) => {
	return request.get(apiPrefix + 'GetVxeTableColumnCacheAsync', { params: params, ...config });
};

export const SetVxeTableColumnCacheAsync = (params: any, config = {}) => {
	return request.post(apiPrefix + 'SetVxeTableColumnCacheAsync', params, config);
};
