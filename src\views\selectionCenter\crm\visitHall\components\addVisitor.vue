<template>
	<el-form ref="ruleFormRef" :model="ruleForm" status-icon label-width="100" class="demo-ruleForm" :rules="rules">
		<el-row :gutter="10" class="mb20">
			<el-col :span="6">
				<el-form-item label="公司名称:" prop="cjName">
					<el-input v-model="ruleForm.cjName" placeholder="公司名称" clearable maxlength="50" />
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="访客姓名:" prop="realName">
					<el-input class="w100" v-model="ruleForm.realName" placeholder="访客姓名" clearable maxlength="50" />
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="访客联系电话:" prop="contactPhone">
					<el-input v-model="ruleForm.contactPhone" placeholder="访客联系电话" clearable maxlength="50" />
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="访客地址:">
					<el-input v-model="ruleForm.cjAddr" placeholder="访客地址" clearable maxlength="50" />
				</el-form-item>
			</el-col>
		</el-row>
		<el-row :gutter="10" class="mb20">
			<el-col :span="6">
				<el-form-item label="到访日期:">
					<dataRange class="publicCss" v-model:date="ruleForm.lastGuestTime" :clearable="false" type="date" />
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="访客类型:" prop="guestType">
					<el-select v-model="ruleForm.guestType" placeholder="访客类型" style="width: 240px">
						<el-option label="未知" :value="0" />
						<el-option label="厂家" :value="1" />
						<el-option label="商家" :value="2" />
						<el-option label="厂家商家一体" :value="3" />
						<el-option label="游客" :value="4" />
					</el-select>
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="主营类目:" prop="mainBusinessCategories">
					<el-input v-model="ruleForm.mainBusinessCategories" placeholder="主营类目" clearable maxlength="50"
						resize="none" />
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="访客意向描述:" prop="intent">
					<el-input type="textarea" row="3" v-model="ruleForm.intent" placeholder="访客意向描述" clearable
						maxlength="200" resize="none" />
				</el-form-item>
			</el-col>
		</el-row>
		<el-row :gutter="10" class="mb20">
			<el-col :span="6">
				<el-form-item label="状态:" prop="status">
					<el-select v-model="ruleForm.status" placeholder="状态" style="width: 240px" @change="changeStatus">
						<el-option label="跟进" value="跟进" />
						<el-option label="丢失" value="丢失" />
						<el-option label="转化" value="转化" />
					</el-select>
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="丢失原因:">
					<el-input type="textarea" row="3" v-model="ruleForm.lostReason" placeholder="丢失原因" clearable
						maxlength="50" :disabled="ruleForm.status != '丢失'" resize="none" />
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="跟进人:">
					<userSelect style="width: 220px" v-model:value="ruleForm.followUserId" :isDdUserId="false"
						v-model:label="ruleForm.followUserName" />
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="接待员:">
					<userSelect style="width: 220px" v-model:value="ruleForm.jieDaiUserId" :isDdUserId="false"
						v-model:label="ruleForm.jieDaiUserName"
						v-if="(!props.info && ruleForm.jieDaiUserId) || props.info" />
				</el-form-item>
			</el-col>
		</el-row>
		<el-row :gutter="10" class="mb20">
			<el-col :span="24">
				<el-form-item label="附件:">
					<uploadMf v-model:imagesStr="ruleForm.attachmentFileList" uploadFormat=".xlsx,.xls,.XLSX,.XLS"
						ref="uploadMfRef" :upstyle="{ height: 40, width: 40 }" :limit="1" uploadName="上传附件"
						:isImage="false" />
				</el-form-item>
			</el-col>
		</el-row>
		<div class="mt10">
			<div style="display: flex; justify-content: center; align-items: center">
				<el-button @click="emit('close')">取消</el-button>
				<el-button type="primary" @click="submitForm(ruleFormRef)">保存</el-button>
			</div>
		</div>
	</el-form>
</template>

<script lang="ts" setup>
import { reactive, ref, defineAsyncComponent, onMounted } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { AddorEditCrmGuest } from '/@/api/customerservice/customerGroup';
import { getUserInfo } from "/@/api/operatemanage/productalllink";
import { formatters } from '/@/utils/vxetableFormats';
import dayjs from 'dayjs';
const uploadMf = defineAsyncComponent(() => import('/@/components/yhCom/uploadMf.vue'));
const userSelect = defineAsyncComponent(() => import('/@/components/yhCom/userSelect.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const emit = defineEmits(['close', 'getList']);
const props = defineProps({
	info: {
		type: Object,
		default: () => null,
	},
});
const uploadMfRef = ref()
const picUrl = ref<string[]>([]);
const userInfo = ref<any>({})
const ruleForm = ref<any>({
	id: null, //主键id
	cjName: null, //厂家名
	realName: null, //真实姓名
	contactPhone: null, //联系电话
	cjAddr: null, //厂家地址
	guestType: 0, //来宾类型
	lastGuestTime: dayjs().format('YYYY-MM-DD'), //到访日期
	mainBusinessCategories: '', //主营类目
	intent: null, //意向描述
	status: '跟进', //状态
	lostReason: null, //丢失原因
	followUserId: null, //跟进人ID
	followUserName: '', //跟进人姓名
	jieDaiUserId: null, //接待人ID
	jieDaiUserName: '', //接待人姓名
	attachmentFile: '', //附件
	attachmentFileList: [] as string[], //附件列表
	customerSource: '1688展厅来访', //客户来源
});
const rules = reactive<FormRules<typeof ruleForm>>({
	cjName: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
	realName: [{ required: true, message: '请输入访客姓名', trigger: 'blur' }],
	contactPhone: [{ required: true, message: '请输入访客联系电话', trigger: 'blur' }],
	guestType: [{ required: true, message: '请选择访客类型', trigger: 'change' }],
	mainBusinessCategories: [{ required: true, message: '请输入主营类目', trigger: 'blur' }],
	intent: [{ required: true, message: '请输入访客意向描述', trigger: 'blur' }],
	status: [{ required: true, message: '请选择状态', trigger: 'change' }],
	customerSource: [{ required: true, message: '请选择客户来源', trigger: 'change' }],
});
const refuploadMf = ref<InstanceType<typeof uploadMf> | null>(null);
const ruleFormRef = ref<FormInstance>();
const submitForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	formEl.validate(async (valid) => {
		if (valid) {
			ruleForm.value.attachmentFile = (uploadMfRef.value.fileList && uploadMfRef.value.fileList?.length > 0) ? JSON.stringify(uploadMfRef.value.fileList) : '';
			const { success } = await AddorEditCrmGuest(ruleForm.value);
			if (success) {
				emit('close');
				emit('getList');
			}
		} else {
			console.log('error submit!');
		}
	});
};

const changeStatus = (val: string) => {
	if (val != '丢失') {
		ruleForm.value.lostReason = null
	}
};

onMounted(async () => {
	if (props.info) {
		ruleForm.value = JSON.parse(JSON.stringify(props.info));
		if (ruleForm.value.attachmentFile) {
			ruleForm.value.attachmentFileList = ruleForm.value.attachmentFile ? [JSON.parse(ruleForm.value.attachmentFile)[0].url] : []
		}
	} else {
		const { data } = await getUserInfo();
		userInfo.value = data
		ruleForm.value.jieDaiUserId = Number(data.id) ?? null
		ruleForm.value.jieDaiUserName = data.nickName
	}
});
</script>
