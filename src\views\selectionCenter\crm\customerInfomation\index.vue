<template>
	<div class="w100 h100">
		<Container>
			<template #header>
				<div class="topCss">
					<el-input v-model="query.keyWords" placeholder="关键字查询,输入姓名,微信,电话等..." class="publicCss"
						style="width: 230px" maxlength="200" clearable />
					<dataRange class="publicCss" v-model:startDate="query.startFirstGuestTime"
						v-model:endDate="query.endFirstGuestTime" style="width: 230px" start-placeholder="首次到访时间"
						end-placeholder="首次到访时间" />
					<dataRange class="publicCss" v-model:startDate="query.startLastGuestTime"
						v-model:endDate="query.endLastGuestTime" style="width: 230px" start-placeholder="末次到访时间"
						end-placeholder="末次到访时间" />
					<dataRange class="publicCss" v-model:startDate="query.startAddTime"
						v-model:endDate="query.endAddTime" style="width: 230px" start-placeholder="登记开始时间"
						end-placeholder="登记结束时间" />
					<dataRange class="publicCss" v-model:startDate="query.startFirstHfTime"
						v-model:endDate="query.endFirstHfTime" style="width: 230px" start-placeholder="首次回访时间"
						end-placeholder="首次回访时间" />
					<dataRange class="publicCss" v-model:startDate="query.startLastHfTime"
						v-model:endDate="query.endLastHfTime" style="width: 230px" start-placeholder="末次回访时间"
						end-placeholder="末次回访时间" />
					<el-select v-model="query.isConvert" placeholder="是否转化" class="publicCss" clearable>
						<el-option v-for="item in isConvertList" :key="item.value" :label="item.label"
							:value="item.value" />
					</el-select>
					<el-select v-model="query.status" placeholder="状态" class="publicCss" clearable>
						<el-option v-for="item in statusList" :key="item.value" :label="item.label"
							:value="item.value" />
					</el-select>
					<el-select v-model="query.guestType" placeholder="来宾类型" class="publicCss" clearable>
						<el-option v-for="item in guestTypeList" :key="item.value" :label="item.label"
							:value="item.value" />
					</el-select>
					<el-select v-model="query.customerSource" placeholder="客户来源" class="publicCss" clearable>
						<el-option v-for="item in customerSourceList" :key="item.value" :label="item.label"
							:value="item.value" />
					</el-select>
					<div class="pb5">
						<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
					</div>
				</div>
			</template>
			<template #content>
				<vxetable ref="table" id="20250608120004" :tableCols="tableCols" order-by="addTime" :is-asc="false"
					showsummary :query="query" :query-api="GetCrmGuestList">
					<template #toolbar_buttons>
						<el-button type="primary" @click="visitorRegistration(true, null)">访客登记</el-button>
					</template>
				</vxetable>
			</template>
		</Container>

		<el-dialog draggable overflow v-model="recordsVisible" title="访客记录" width="60%" :close-on-click-modal="false">
			<visitorRecords v-if="recordsVisible" style="height: 600px" :fkId="fkId" />
		</el-dialog>

		<el-dialog draggable overflow v-model="addVisitorVisible" :title="isAdd ? '新增访客' : '编辑访客'" width="60%"
			:close-on-click-modal="false">
			<addVisitor v-if="addVisitorVisible" :info="fkForm" @close="addVisitorVisible = false"
				@getList="table.refreshTable(true)" />
		</el-dialog>

		<el-dialog draggable overflow v-model="addRecordsVisible" title="新增访客记录" width="20%"
			:close-on-click-modal="false">
			<addRecordsLog v-if="addRecordsVisible" @close="addRecordsVisible = false" :guestId="guestId"
				@getList="table.refreshTable(true)" />
		</el-dialog>

		<el-dialog draggable overflow v-model="returnVisible" title="回访记录" width="60%" :close-on-click-modal="false">
			<returnVisitRecords v-if="returnVisible" style="height: 600px" :hfId="hfId" />
		</el-dialog>

		<el-dialog draggable overflow v-model="addReturnVisible" title="新增回访记录" width="20%"
			:close-on-click-modal="false">
			<addReturnRecordsLog v-if="addReturnVisible" @close="addReturnVisible = false" :guestId="guestId"
				@getList="table.refreshTable(true)" />
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent } from 'vue';
import dayjs from 'dayjs';
import { platformlist } from '/@/utils/tools';
import { GetCrmGuestList } from '/@/api/customerservice/customerGroup';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
const visitorRecords = defineAsyncComponent(() => import('./components/visitorRecords.vue'));
const returnVisitRecords = defineAsyncComponent(() => import('./components/returnVisitRecords.vue'));
const addRecordsLog = defineAsyncComponent(() => import('./components/addRecordsLog.vue'));
const addReturnRecordsLog = defineAsyncComponent(() => import('./components/addReturnRecordsLog.vue'));
const addVisitor = defineAsyncComponent(() => import('./components/addVisitor.vue'));
const recordsVisible = ref(false);
const returnVisible = ref(false);
const addRecordsVisible = ref(false);
const addReturnVisible = ref(false);
const addVisitorVisible = ref(false);
const query = ref({
	keyWords: '',
	startFirstGuestTime: '',
	endFirstGuestTime: '',
	startLastGuestTime: '',
	endLastGuestTime: '',
	startFirstAddTime: '',
	endFirstAddTime: '',
	startAddTime: '',
	endAddTime: '',
	startFirstHfTime: '',
	endFirstHfTime: '',
	startLastHfTime: '',
	endLastHfTime: '',
	sex: '',
	isConvert: '',
	guestType: '',
	status: '',
	customerSource: ''
});
const sexList = ref([
	{ label: '男', value: 1 },
	{ label: '女', value: 2 },
	{ label: '未录入', value: 0 },
]);
const isConvertList = ref([
	{ label: '已转化', value: 1 },
	{ label: '未转化', value: 0 },
]);
const guestTypeList = ref([
	{ label: '未知', value: 0 },
	{ label: '厂家', value: 1 },
	{ label: '商家', value: 2 },
	{ label: '厂家商家一体', value: 3 },
	{ label: '游客', value: 4 },
]);
const statusList = ref([
	{ label: '跟进', value: '跟进' },
	{ label: '丢失', value: '丢失' },
	{ label: '转化', value: '转化' },
]);
const customerSourceList = ref([
	{ label: '1688展厅来访', value: '1688展厅来访' },
	{ label: '业务外出', value: '业务外出' },
]);
const fkForm = ref({});
const fkId = ref('');
const guestId = ref('');
const hfId = ref('');
const table = ref();
const isAdd = ref(false);
const visitorLog = (row: any) => {
	fkId.value = row.id;
	recordsVisible.value = true;
};
const addVisitorLog = (row: any) => {
	guestId.value = row.id;
	addRecordsVisible.value = true;
};
const returnVisityLog = (row: any) => {
	hfId.value = row.id;
	returnVisible.value = true;
};
const addReturnVisityLog = (row: any) => {
	guestId.value = row.id;
	addReturnVisible.value = true;
};
const visitorRegistration = (type: boolean, row: any) => {
	isAdd.value = type;
	fkForm.value = row;
	addVisitorVisible.value = true;
};
const tableCols = ref<VxeTable.Columns[]>([
	{ width: 80, align: 'center', sortable: true, field: 'realName', title: '姓名', fixed: 'left' },
	{ width: 100, sortable: true, field: 'customerSource', title: '客户来源', fixed: 'left' },
	{ width: 90, sortable: true, field: 'contactPhone', title: '联系电话', fixed: 'left' },
	{ width: 70, field: 'picture', title: '定位图片', type: 'image', fixed: 'left' },
	{ width: 120, field: 'positioning', title: '定位', fixed: 'left' },
	{
		width: 80,
		sortable: true,
		field: 'isConvert',
		title: '是否转化',
		formatter: (row: any) => (row.isConvert !== null ? isConvertList.value.find((item: any) => item.value == row.isConvert)?.label : row.isConvert),
	},
	{ width: 70, align: 'center', sortable: true, field: 'status', title: '状态', formatter: (row: any) => (row.status !== null ? statusList.value.find((item: any) => item.value == row.status)?.label : row.status) },
	{ width: 80, sortable: true, field: 'contactWechat', title: '微信' },
	{
		width: 80,
		sortable: true,
		align: 'center',
		field: 'guestType',
		title: '来宾类型',
		formatter: (row: any) => (row.guestType !== null ? guestTypeList.value.find((item: any) => item.value == row.guestType)?.label : row.guestType),
	},
	{ width: 80, sortable: true, field: 'cjName', title: '厂家名' },
	{ width: 90, sortable: true, field: 'cjAddr', title: '厂家地址' },
	{ width: 90, sortable: true, field: 'intent', title: '意向描述' },
	{ width: 90, sortable: true, field: 'lostReason', title: '丢失原因' },
	{ width: 80, sortable: true, field: 'remark1', title: '备注1' },
	{ width: 80, sortable: true, field: 'remark2', title: '备注2' },
	{ width: 80, sortable: true, field: 'remark3', title: '备注3' },
	{ width: 90, sortable: true, field: 'guestTimes', title: '来访次数' },
	{ width: 120, sortable: true, field: 'firstGuestTime', title: '首次到访时间', formatter: 'formatDate', align: 'center' },
	{ width: 120, sortable: true, field: 'lastGuestTime', title: '末次到访时间', formatter: 'formatDate', align: 'center' },
	{ width: 105, sortable: true, field: 'addUserName', title: '登记人姓名' },
	{ width: 120, sortable: true, field: 'addTime', title: '登记时间', formatter: 'formatDate', align: 'center' },
	{ width: 105, sortable: true, field: 'editUserName', title: '修改人姓名' },
	{ width: 120, sortable: true, field: 'editTime', title: '修改时间', formatter: 'formatDate', align: 'center' },
	{ width: 90, sortable: true, field: 'hfTimes', title: '回访次数' },
	{ width: 120, sortable: true, field: 'firstHfTime', title: '首次回访时间', formatter: 'formatDate', align: 'center' },
	{ width: 120, sortable: true, field: 'firstHfUserName', title: '首次回访人名' },
	{ width: 120, sortable: true, field: 'lastHfTime', title: '末次回访时间', formatter: 'formatDate', align: 'center' },
	{ width: 120, sortable: true, field: 'lastHfUserName', title: '末次回访人名' },
	{
		width: 350,
		field: '_20250608115955',
		title: '操作',
		type: 'btnList',
		align: 'center',
		fixed: 'right',
		btnList: [
			{ title: '编辑访客', handle: (row: any) => visitorRegistration(false, row) },
			{ title: '访客记录', handle: (row: any) => visitorLog(row) },
			{ title: '新增访客记录', handle: (row: any) => addVisitorLog(row) },
			{ title: '回访记录', handle: (row: any) => returnVisityLog(row) },
			{ title: '新增回访记录', handle: (row: any) => addReturnVisityLog(row) },
		],
	},
]);
</script>

<style scoped lang="scss"></style>
