<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange class="publicCss" v-model:startDate="query.startTime" v-model:endDate="query.endTime" style="width: 200px" :clearable="false" />
				<div class="publicCss">
					<manyInput v-model:inputt="query.yeWus" title="业务员" :verifyNumber="false" placeholder="请输入业务员" :maxRows="100" :maxlength="1000" />
				</div>
				<el-select v-model="query.deptIds" placeholder="小组" class="publicCss" multiple collapse-tags clearable>
					<el-option v-for="item in deptList" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<dataRange class="publicCss" v-model:startDate="query.ruZhiStartTime" v-model:endDate="query.ruZhiEndTime" style="width: 200px" start-placeholder="入职时间" end-placeholder="入职时间" />
				<numberRangeByOperator
					title="拜访数量"
					style="margin: 0 5px 5px 0"
					v-model:max-num="query.maxbaifangCount"
					v-model:min-num="query.minbaifangCount"
					v-model:operator="query.baifangComparisonTypes"
					:max="999999"
					:min="0.001"
					:precision="3"
				/>
				<numberRangeByOperator
					title="拉群数量"
					style="margin: 0 5px 5px 0"
					v-model:max-num="query.maxLaQunCount"
					v-model:min-num="query.minLaQunCount"
					v-model:operator="query.laQunCountComparisonTypes"
					:max="999999"
					:min="0.001"
					:precision="3"
				/>
				<numberRangeByOperator
					title="跟进数量"
					style="margin: 0 5px 5px 0"
					v-model:max-num="query.maxgenJinCount"
					v-model:min-num="query.mingenJinCount"
					v-model:operator="query.genJinComparisonTypes"
					:max="999999"
					:min="0.001"
					:precision="3"
				/>
				<numberRangeByOperator
					title="丢失数量"
					style="margin: 0 5px 5px 0"
					v-model:max-num="query.maxdiushiCount"
					v-model:min-num="query.mindiushiCount"
					v-model:operator="query.diushiComparisonTypes"
					:max="999999"
					:min="0.001"
					:precision="3"
				/>
				<numberRangeByOperator
					title="入仓工厂数量"
					style="margin: 0 5px 5px 0"
					v-model:max-num="query.maxRuCangGongChangCount"
					v-model:min-num="query.minRuCangGongChangCount"
					v-model:operator="query.ruCangGongChangCountComparisonTypes"
					:max="999999"
					:min="0.001"
					:precision="3"
				/>
				<numberRangeByOperator
					title="入仓SPU数量"
					style="margin: 0 5px 5px 0"
					v-model:max-num="query.maxRuCangSpuCount"
					v-model:min-num="query.minRuCangSpuCount"
					v-model:operator="query.ruCangSpuCountComparisonTypes"
					:max="999999"
					:min="0.001"
					:precision="3"
				/>
				<numberRangeByOperator
					title="入仓SKU数量"
					style="margin: 0 5px 5px 0"
					v-model:max-num="query.maxRuCangSkuCount"
					v-model:min-num="query.minRuCangSkuCount"
					v-model:operator="query.ruCangSkuCountComparisonTypes"
					:max="999999"
					:min="0.001"
					:precision="3"
				/>
				<numberRangeByOperator
					title="拜访拉群率"
					style="margin: 0 5px 5px 0"
					v-model:max-num="query.maxbaifanglaqunRate"
					v-model:min-num="query.minbaifanglaqunRate"
					v-model:operator="query.baifanglaqunRateComparisonTypes"
					:max="999999"
					:min="0.001"
					:precision="3"
				/>
				<numberRangeByOperator
					title="拉群入仓率"
					style="margin: 0 5px 5px 0"
					v-model:max-num="query.maxlaqunrucangRate"
					v-model:min-num="query.minlaqunrucangRate"
					v-model:operator="query.laqunrucangRateComparisonTypes"
					:max="999999"
					:min="0.001"
					:precision="3"
				/>
				<numberRangeByOperator
					title="拜访入仓率"
					style="margin: 0 5px 5px 0"
					v-model:max-num="query.maxbaifangrucangRate"
					v-model:min-num="query.minbaifangrucangRate"
					v-model:operator="query.baifangrucangRateComparisonTypes"
					:max="999999"
					:min="0.001"
					:precision="3"
				/>
				<numberRangeByOperator
					title="日均拜访量"
					style="margin: 0 5px 5px 0"
					v-model:max-num="query.maxrijunbaifangliang"
					v-model:min-num="query.minrijunbaifangliang"
					v-model:operator="query.rijunbaifangliangComparisonTypes"
					:max="999999"
					:min="0.001"
					:precision="3"
				/>
				<numberRangeByOperator
					title="日均拉群数量"
					style="margin: 0 5px 5px 0"
					v-model:max-num="query.maxrijunlaqunshuliang"
					v-model:min-num="query.minrijunlaqunshuliang"
					v-model:operator="query.rijunlaqunshuliangComparisonTypes"
					:max="999999"
					:min="0.001"
					:precision="3"
				/>
				<numberRangeByOperator
					title="日均入驻仓工厂数量"
					style="margin: 0 5px 5px 0"
					v-model:max-num="query.maxrijunruzhucanggongchangcount"
					v-model:min-num="query.minrijunruzhucanggongchangcount"
					v-model:operator="query.rijunruzhucanggongchangcountComparisonTypes"
					:max="999999"
					:min="0.001"
					:precision="3"
				/>
				<numberRangeByOperator
					title="工厂平均SPU数量"
					style="margin: 0 5px 5px 0"
					v-model:max-num="query.maxGongChangPingJunSPUCount"
					v-model:min-num="query.minGongChangPingJunSPUCount"
					v-model:operator="query.gongChangPingJunSPUCountComparisonTypes"
					:max="999999"
					:min="0.001"
					:precision="3"
				/>
				<numberRangeByOperator
					title="工厂平均SKU数量"
					style="margin: 0 5px 5px 0"
					v-model:max-num="query.maxGongChangPingJunSKUCount"
					v-model:min-num="query.minGongChangPingJunSKUCount"
					v-model:operator="query.gongChangPingJunSKUCountComparisonTypes"
					:max="999999"
					:min="0.001"
					:precision="3"
				/>

				<div class="pb5">
					<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				id="20250714100618"
				:tableCols="tableCols"
				showsummary
				isIndexFixed
				:query="query"
				:query-api="GetCrmEfficiencyStatisticsList"
				:export-api="ExportCrmEfficiencyStatisticsList"
				:asyncExport="{ title: '产业带开发组效率统计', isAsync: false }"
			>
			</vxetable>

			<el-dialog v-model="drawer" title="趋势图" width="70%" draggable overflow>
				<lineChart :chartData="chartData" ref="lineChartRef" />
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted, nextTick } from 'vue';
import dayjs from 'dayjs';
import { GetCrmEfficiencyStatisticsList, ExportCrmEfficiencyStatisticsList, QueryCrmEfficiencyStatisticsAnalysis, GetCrmEfficiencyDept } from '/@/api/customerservice/customerGroup';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const numberRangeByOperator = defineAsyncComponent(() => import('/@/components/yhCom/numberRangeByOperator.vue'));
const manyInput = defineAsyncComponent(() => import('/@/components/yhCom/manyInput.vue'));
const lineChart = defineAsyncComponent(() => import('/@/components/yhCom/lineChart.vue'));
const query = ref({
	startTime: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
	endTime: dayjs().format('YYYY-MM-DD'),
	deptIds: [],
	ruZhiStartTime: null,
	ruZhiEndTime: null,
	yeWus: '', // 业务员
	minbaifangCount: null,
	maxbaifangCount: null,
	baifangComparisonTypes: null,
	minLaQunCount: null,
	maxLaQunCount: null,
	laQunCountComparisonTypes: null,
	mingenJinCount: null,
	maxgenJinCount: null,
	genJinComparisonTypes: null,
	mindiushiCount: null,
	maxdiushiCount: null,
	diushiComparisonTypes: null,
	minRuCangGongChangCount: null,
	maxRuCangGongChangCount: null,
	ruCangGongChangCountComparisonTypes: null,
	minRuCangSpuCount: null,
	maxRuCangSpuCount: null,
	ruCangSpuCountComparisonTypes: null,
	minRuCangSkuCount: null,
	maxRuCangSkuCount: null,
	ruCangSkuCountComparisonTypes: null,
	minbaifanglaqunRate: null,
	maxbaifanglaqunRate: null,
	baifanglaqunRateComparisonTypes: null,
	minlaqunrucangRate: null,
	maxlaqunrucangRate: null,
	laqunrucangRateComparisonTypes: null,
	minrijunbaifangliang: null,
	maxrijunbaifangliang: null,
	rijunbaifangliangComparisonTypes: null,
	minrijunlaqunshuliang: null,
	maxrijunlaqunshuliang: null,
	rijunlaqunshuliangComparisonTypes: null,
	minrijunruzhucanggongchangcount: null,
	maxrijunruzhucanggongchangcount: null,
	rijunruzhucanggongchangcountComparisonTypes: null,
	minGongChangPingJunSPUCount: null,
	maxGongChangPingJunSPUCount: null,
	gongChangPingJunSPUCountComparisonTypes: null,
	minGongChangPingJunSKUCount: null,
	maxGongChangPingJunSKUCount: null,
	gongChangPingJunSKUCountComparisonTypes: null,
	maxbaifangrucangRate: null,
	minbaifangrucangRate: null,
	baifangrucangRateComparisonTypes: null,
});
const drawer = ref(false);
const chartData = ref({});
const lineChartRef = ref();
const table = ref();
const deptList = ref<Public.options[]>([]);
const showCharts = async (row: any) => {
	const { data } = await QueryCrmEfficiencyStatisticsAnalysis({
		startTime: query.value.startTime,
		endTime: query.value.endTime,
		yeWus: row.userName,
	});
	chartData.value = data;
	drawer.value = true;
	nextTick(() => {
		lineChartRef.value.reSetChart(data);
	});
	console.log(data, 'data');
};
const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, align: 'right', width: 100, field: 'yearMonthDay', title: '日期', formatter: 'formatDate' },
	{ sortable: true, align: 'right', width: 100, field: 'full_name', title: '小组' },
	{ sortable: true, align: 'right', width: 100, field: 'userName', title: '业务员' },
	{ sortable: true, align: 'right', width: 100, field: 'hired_date', title: '入职日期', formatter: 'formatDate' },
	// { sortable: true, align: 'right', width: 100, field: 'zaizhitianshu', title: '在职天数', formatter: 'fmtAmt0' },
	{ sortable: true, align: 'right', width: 100, field: 'chuqintianshu', title: '出勤天数', formatter: 'fmtAmt0' },
	{ sortable: true, align: 'right', width: 100, field: 'baifangCount', title: '拜访数量', formatter: 'fmtAmt0' },
	{ sortable: true, align: 'right', width: 100, field: 'laQunCount', title: '拉群数量', formatter: 'fmtAmt0' },
	{ sortable: true, align: 'right', width: 100, field: 'genJinCount', title: '跟进数量', formatter: 'fmtAmt0' },
	{ sortable: true, align: 'right', width: 100, field: 'diushiCount', title: '丢失数量', formatter: 'fmtAmt0' },
	{ sortable: true, align: 'right', width: 100, field: 'ruCangGongChangCount', title: '入仓工厂数量', formatter: 'fmtAmt0' },
	{ sortable: true, align: 'right', width: 100, field: 'ruCangSpuCount', title: '入仓spu数量', formatter: 'fmtAmt0' },
	{ sortable: true, align: 'right', width: 100, field: 'ruCangSkuCount', title: '入仓sku数量', formatter: 'fmtAmt0' },
	{
		sortable: true,
		align: 'right',
		width: 100,
		field: 'baifanglaqunRate',
		title: '拜访拉群率',
		formatter: (row: any) => (row.baifanglaqunRate !== null ? row.baifanglaqunRate + '%' : row.baifanglaqunRate),
	},
	{
		sortable: true,
		align: 'right',
		width: 100,
		field: 'laqunrucangRate',
		title: '拉群入仓率',
		formatter: (row: any) => (row.laqunrucangRate !== null ? row.laqunrucangRate + '%' : row.laqunrucangRate),
	},
	{
		sortable: true,
		align: 'right',
		width: 100,
		field: 'baifangrucangRate',
		title: '拜访入仓率',
		formatter: (row: any) => (row.baifangrucangRate !== null ? row.baifangrucangRate + '%' : row.baifangrucangRate),
	},
	{ sortable: true, align: 'right', width: 100, field: 'rijunbaifangliang', title: '日均拜访数量', formatter: 'fmtAmt2' },
	{ sortable: true, align: 'right', width: 100, field: 'rijunlaqunshuliang', title: '日均拉群数量', formatter: 'fmtAmt2' },
	{ sortable: true, align: 'right', width: 100, field: 'rijunruzhucanggongchangcount', title: '日均入驻仓工厂数量', formatter: 'fmtAmt2' },
	{ sortable: true, align: 'right', width: 100, field: 'gongChangPingJunSpuCount', title: '工厂平均spu数量', formatter: 'fmtAmt2' },
	{ sortable: true, align: 'right', width: 100, field: 'gongChangPingJunSkuCount', title: '工厂平均sku数量', formatter: 'fmtAmt2' },
	{
		title: '趋势图',
		align: 'center',
		width: '100',
		type: 'btnList',
		field: '_20250714104535',
		minWidth: '100',
		btnList: [{ title: '趋势图', handle: (row: any) => showCharts(row) }],
		fixed: 'right',
	},
]);

const getDept = async () => {
	const { data } = await GetCrmEfficiencyDept();
	console.log(data, 'data');
	deptList.value = data.map((item: any) => {
		return {
			label: item.full_name,
			value: item.dept_id,
		};
	});
};

onMounted(() => {
	getDept();
});
</script>

<style scoped lang="scss">
:deep(.el-select__tags-text) {
	max-width: 40px;
}
</style>
