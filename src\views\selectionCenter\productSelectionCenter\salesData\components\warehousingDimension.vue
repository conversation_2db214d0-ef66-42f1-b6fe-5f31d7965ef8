<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange class="publicCss" @change="changeTime" v-model:startDate="query.dayReportStartDate" v-model:endDate="query.dayReportEndDate" style="width: 200px" :clearable="false" />
				<div class="pb5">
					<el-button type="primary" @click="getList">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<div style="height: 350px">
				<vxetable
					ref="table"
					id="202506091055"
					:tableCols="tableCols"
					showsummary
					isIndexFixed
					:query="query"
					:query-api="GetSelectionProductCenterSaleDataPlanA"
					@footerCellClick="onSummaryTotalMap"
					:export-api="ExportSelectionProductCenterSaleDataPlanA"
					:asyncExport="{
						title: `选品中心销售数据信息 ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`,
						isAsync: false,
					}"
				></vxetable>
			</div>
			<div style="height: 375px">
				<warehousingStorage @metricClick="handleMetricClick" />
			</div>
			<el-dialog v-model="codingInfo.visible" title="款式编码详情" width="70%" draggable overflow>
				<warehousingDetails
					v-if="codingInfo.visible"
					:options="{
						rowData: codingInfo.rowData,
						fieldName: codingInfo.fieldName,
						isSummary: codingInfo.isSummary,
						parentData: query,
						operationsTeamList,
					}"
				/>
			</el-dialog>
			<el-dialog v-model="totalMapVisible" width="60%" draggable overflow>
				<div>
					<dataRange
						v-model:startDate="trendChart.dayReportStartDate"
						v-model:endDate="trendChart.dayReportEndDate"
						:clearable="false"
						startPlaceholder="开始时间"
						endPlaceholder="结束时间"
						style="width: 260px"
						@change="onTrendMethod({})"
					/>
					<lineChart
						v-if="totalMapVisible"
						:chartData="analysisData"
						ref="sumChart"
						:thisStyle="{
							width: '100%',
							height: '600px',
							'box-sizing': 'border-box',
							'line-height': '600px',
						}"
						:tooltipFormatter="customTooltipFormatter"
					/>
				</div>
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted, nextTick } from 'vue';
import dayjs from 'dayjs';
const lineChart = defineAsyncComponent(() => import('/@/components/yhCom/lineChart.vue'));
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
import {
	GetSelectionProductCenterSaleDataPlanA,
	QuerySelectionProductCenterSaleDataPlanAAnalysis,
	ExportSelectionProductCenterSaleDataPlanA,
} from '/@/api/bookkeeper/selectionProductCenterSaleDataPlanA';
import { GetDirectorGroupList } from '/@/api/operatemanage/shopmanager';
import warehousingDetails from './warehousingDetails.vue';
import warehousingStorage from './warehousingStorage.vue';

// 定义emit事件
const emit = defineEmits(['metricClick']);

const operationsTeamList = ref([]);

// 处理指标点击事件
const handleMetricClick = (team: any, type: string) => {
	emit('metricClick', team, type);
};
const query = ref({
	dayReportStartDate: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
	dayReportEndDate: dayjs().format('YYYY-MM-DD'),
	startDate: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
	dataType: 2, //入仓维度
});
const table = ref();
const codingInfo = ref({
	visible: false,
	rowData: {},
	fieldName: '',
	isSummary: false,
});
const totalMapVisible = ref(false);
const trendChart = ref({
	dayReportStartDate: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
	dayReportEndDate: dayjs().format('YYYY-MM-DD'),
	modeType: '',
});
const analysisData = ref([]);
const sumChart = ref();

const customTooltipFormatter = (params: any) => {
	let result = params[0].name + '<br/>';
	params.forEach((param: any) => {
		const seriesName = param.seriesName;
		const value = param.value;
		const color = param.color;
		const percentageFields = ['毛六利润率'];
		const displayValue = percentageFields.includes(seriesName) ? `${value}%` : value;
		result += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
		result += `${seriesName}: ${displayValue}<br/>`;
	});
	return result;
};

const changeTime = (e: any) => {
	query.value.startDate = e ? e[0] : null;
};

const getList = () => {
	query.value.startDate = query.value.dayReportStartDate;
	table.value.refreshTable(true);
};

const onDetailsMethod = (row: any, field: string) => {
	codingInfo.value.visible = true;
	codingInfo.value.rowData = row;
	codingInfo.value.fieldName = field;
	codingInfo.value.isSummary = false;
};

const onSummaryTotalMap = async (row: any, column: any) => {
	codingInfo.value.visible = true;
	codingInfo.value.rowData = {};
	codingInfo.value.fieldName = column;
	codingInfo.value.isSummary = true;
};

const onTrendMethod = async (row: any) => {
	let startTime;
	let endTime;
	if (row && row.modeType !== undefined) {
		trendChart.value.modeType = row.modeType;
		trendChart.value.dayReportStartDate = query.value.dayReportStartDate;
		trendChart.value.dayReportEndDate = query.value.dayReportEndDate;
		startTime = dayjs(query.value.dayReportStartDate).format('YYYY-MM-DD');
		endTime = dayjs(query.value.dayReportEndDate).format('YYYY-MM-DD');
	} else {
		startTime = dayjs(trendChart.value.dayReportStartDate).format('YYYY-MM-DD');
		endTime = dayjs(trendChart.value.dayReportEndDate).format('YYYY-MM-DD');
	}
	const params = {
		dayReportStartDate: startTime,
		dayReportEndDate: endTime,
		startDate: startTime,
		modeType: trendChart.value.modeType,
		dataType: query.value.dataType, //编码维度
	};

	const { data, success } = await QuerySelectionProductCenterSaleDataPlanAAnalysis(params);
	if (success) {
		analysisData.value = data;
		totalMapVisible.value = true;
		nextTick(() => {
			if (sumChart.value) {
				sumChart.value.reSetChart(data);
			}
		});
	}
};

const defaultFormatter = (field: string) => (row: any) => {
	// 添加千分位格式化函数
	const formatNumber = (num: number) => {
		return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
	};
	if (field == 'profit6Rate') {
		return row[field] ? row[field].toFixed(2) + '%' : '0%';
	}
	// 金额类字段，需要千分位格式化
	const amountFields = ['saleAmount', 'profit6'];
	if (amountFields.includes(field)) {
		return row[field] ? formatNumber(parseFloat(row[field])) : '0.00';
	}
	// 数量类字段，整数千分位格式化
	const countFields = ['styleCodeCount', 'goodsCodeCount', 'onLineIdCount', 'orderCount'];
	if (countFields.includes(field)) {
		return row[field] ? parseInt(row[field]).toLocaleString('zh-CN') : '0';
	}
	return row[field] ? row[field] : '0';
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'modeType', title: '入仓模式', align: 'center', formatter: (row: any) => (row.modeType && row.modeType == '自费采购' ? '采购入仓' : row.modeType) },
	{
		summaryEvent: true,
		sortable: true,
		field: 'styleCodeCount',
		title: '款式编码数',
		align: 'right',
		formatter: defaultFormatter('styleCodeCount'),
		type: 'click',
		handle: (row: any) => onDetailsMethod(row, 'styleCodeCount'),
	},
	{
		summaryEvent: true,
		sortable: true,
		field: 'goodsCodeCount',
		title: '商品编码数',
		align: 'right',
		formatter: defaultFormatter('goodsCodeCount'),
		type: 'click',
		handle: (row: any) => onDetailsMethod(row, 'goodsCodeCount'),
	},
	{
		summaryEvent: true,
		sortable: true,
		field: 'onLineIdCount',
		title: '上架ID数',
		align: 'right',
		formatter: defaultFormatter('onLineIdCount'),
		type: 'click',
		handle: (row: any) => onDetailsMethod(row, 'onLineIdCount'),
	},
	{
		summaryEvent: true,
		sortable: true,
		field: 'orderCount',
		title: '订单量',
		align: 'right',
		formatter: defaultFormatter('orderCount'),
		type: 'click',
		handle: (row: any) => onDetailsMethod(row, 'orderCount'),
	},
	{
		summaryEvent: true,
		sortable: true,
		field: 'saleAmount',
		title: '销售金额',
		align: 'right',
		formatter: defaultFormatter('saleAmount'),
		type: 'click',
		handle: (row: any) => onDetailsMethod(row, 'saleAmount'),
	},
	{
		summaryEvent: true,
		sortable: true,
		field: 'profit6',
		title: '毛六利润(发生)',
		align: 'right',
		formatter: defaultFormatter('profit6'),
		type: 'click',
		handle: (row: any) => onDetailsMethod(row, 'profit6'),
	},
	{
		summaryEvent: true,
		sortable: true,
		field: 'profit6Rate',
		title: '毛六利润率(发生)',
		align: 'right',
		formatter: defaultFormatter('profit6Rate'),
		type: 'click',
		handle: (row: any) => onDetailsMethod(row, 'profit6Rate'),
	},
	{
		align: 'center',
		width: '100',
		type: 'btnList',
		field: 'action',
		minWidth: '100',
		fixed: 'right',
		btnList: [{ title: '趋势图', handle: (row) => onTrendMethod(row) }],
	},
]);
onMounted(() => {
	GetDirectorGroupList({}).then((res) => {
		operationsTeamList.value = res.data || [];
	});
});
</script>

<style scoped lang="scss"></style>
