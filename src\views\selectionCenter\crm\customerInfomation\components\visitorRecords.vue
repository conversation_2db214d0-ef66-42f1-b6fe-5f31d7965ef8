<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange class="publicCss" v-model:startDate="query.startGuestTime"
					v-model:endDate="query.endGuestTime" style="width: 230px" start-placeholder="到访时间"
					end-placeholder="到访时间" />
				<dataRange class="publicCss" v-model:startDate="query.startAddTime" v-model:endDate="query.endAddTime"
					style="width: 230px" start-placeholder="登记开始时间" end-placeholder="登记结束时间" />
				<div class="pb5">
					<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="20250313094053" orderBy="addTime" :is-asc="false" :tableCols="tableCols"
				showsummary isIndexFixed :query="query" :query-api="GetCrmGuestRecordList"
				:asyncExport="{ title: '现金流', isAsync: false }">
			</vxetable>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent } from 'vue';
import { GetCrmGuestRecordList } from '/@/api/customerservice/customerGroup';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
import { platformlist } from '/@/utils/tools';
const props = defineProps({
	fkId: {
		type: String,
		default: '',
	},
});
const query = ref({
	fkId: props.fkId,
	startGuestTime: '',
	endGuestTime: '',
	startAddTime: '',
	endAddTime: '',
});
const table = ref();
const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'guestTime', title: '到访时间', formatter: 'formatDate' },
	{ sortable: true, field: 'guestIntent', title: '来访意向' },
	{ sortable: true, field: 'jdRemark', title: '接待人备注' },
	{ sortable: true, field: 'jdUserName', title: '接待人姓名' },
	{ sortable: true, field: 'addTime', title: '登记时间', formatter: 'formatDate' },
]);
</script>

<style scoped lang="scss"></style>
