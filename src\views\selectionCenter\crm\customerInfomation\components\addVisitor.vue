<template>
	<el-form ref="ruleFormRef" :model="ruleForm" status-icon label-width="90" class="demo-ruleForm" :rules="rules">
		<el-row :gutter="10" class="mb20">
			<el-col :span="6">
				<el-form-item label="姓名:" prop="realName">
					<el-input class="w100" v-model="ruleForm.realName" placeholder="姓名" clearable maxlength="50" />
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="联系电话:" prop="contactPhone">
					<el-input v-model="ruleForm.contactPhone" placeholder="联系电话" clearable maxlength="50" />
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="客户来源:" prop="customerSource">
					<el-select v-model="ruleForm.customerSource" placeholder="客户来源" class="publicCss" clearable>
						<el-option v-for="item in customerSourceList" :key="item.value" :label="item.label"
							:value="item.value" />
					</el-select>
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="微信:">
					<el-input v-model="ruleForm.contactWechat" placeholder="微信" clearable maxlength="50" />
				</el-form-item>
			</el-col>
		</el-row>
		<el-row :gutter="10" class="mb20">
			<el-col :span="6">
				<el-form-item label="状态:">
					<el-select v-model="ruleForm.status" placeholder="状态" style="width: 240px"
						@change="ruleForm.status != '丢失' ? (ruleForm.lostReason = null) : ''">
						<el-option label="跟进" value="跟进" />
						<el-option label="丢失" value="丢失" />
						<el-option label="转化" value="转化" />
					</el-select>
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="是否转化:">
					<el-select v-model="ruleForm.isConvert" placeholder="是否转化" style="width: 240px">
						<el-option label="已转化" :value="1" />
						<el-option label="未转化" :value="0" />
					</el-select>
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="来宾类型:">
					<el-select v-model="ruleForm.guestType" placeholder="来宾类型" style="width: 240px">
						<el-option label="未知" :value="0" />
						<el-option label="厂家" :value="1" />
						<el-option label="商家" :value="2" />
						<el-option label="厂家商家一体" :value="3" />
						<el-option label="游客" :value="4" />
					</el-select>
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="厂家名:">
					<el-input v-model="ruleForm.cjName" placeholder="厂家名" clearable maxlength="50" />
				</el-form-item>
			</el-col>
		</el-row>
		<el-row :gutter="10" class="mb20">
			<el-col :span="6">
				<el-form-item label="厂家地址:">
					<el-input v-model="ruleForm.cjAddr" placeholder="厂家地址" clearable maxlength="50" />
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="意向描述:">
					<el-input type="textarea" row="3" v-model="ruleForm.intent" placeholder="意向描述" clearable
						maxlength="4000" resize="none" />
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="丢失原因:">
					<el-input type="textarea" row="3" v-model="ruleForm.lostReason" placeholder="丢失原因" clearable
						maxlength="50" :disabled="ruleForm.status != '丢失'" resize="none" />
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="备注1:">
					<el-input type="textarea" row="3" v-model="ruleForm.remark1" placeholder="备注1" clearable
						maxlength="4000" resize="none" />
				</el-form-item>
			</el-col>
		</el-row>
		<el-row :gutter="10" class="mb20">
			<el-col :span="6">
				<el-form-item label="备注2:">
					<el-input type="textarea" row="3" v-model="ruleForm.remark2" placeholder="备注2" clearable
						maxlength="4000" resize="none" />
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="备注3:">
					<el-input type="textarea" row="3" v-model="ruleForm.remark3" placeholder="备注3" clearable
						maxlength="4000" resize="none" />
				</el-form-item>
			</el-col>
		</el-row>
		<el-row :gutter="10" class="mb20">
			<el-col :span="6">
				<el-form-item label="来访次数:">
					{{ ruleForm.guestTimes }}
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="首次到访时间:">
					{{ formatDate(ruleForm.firstGuestTime) }}
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="末次到访时间:">
					{{ formatDate(ruleForm.lastGuestTime) }}
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="登记人姓名:">
					{{ ruleForm.addUserName }}
				</el-form-item>
			</el-col>
		</el-row>
		<el-row :gutter="10" class="mb20">
			<el-col :span="6">
				<el-form-item label="登记时间:">
					{{ formatDate(ruleForm.addTime) }}
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="修改人姓名:">
					{{ ruleForm.editUserName }}
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="修改时间:">
					{{ formatDate(ruleForm.editTime) }}
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="回访次数:">
					{{ ruleForm.hfTimes }}
				</el-form-item>
			</el-col>
		</el-row>
		<el-row :gutter="10" class="mb20">
			<el-col :span="6">
				<el-form-item label="首次回访时间:">
					{{ formatDate(ruleForm.firstHfTime) }}
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="首次回访人名:">
					{{ ruleForm.firstHfUserName }}
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="末次回访时间:">
					{{ formatDate(ruleForm.lastHfTime) }}
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="末次回访人名:">
					{{ ruleForm.lastHfUserName }}
				</el-form-item>
			</el-col>
		</el-row>
		<div class="mt10">
			<div style="display: flex; justify-content: center; align-items: center">
				<el-button @click="emit('close')">取消</el-button>
				<el-button type="primary" @click="submitForm(ruleFormRef)">保存</el-button>
			</div>
		</div>
	</el-form>
</template>

<script lang="ts" setup>
import { reactive, ref, defineAsyncComponent, onMounted } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { AddorEditCrmGuest } from '/@/api/customerservice/customerGroup';
import { formatters } from '/@/utils/vxetableFormats';
const uploadMf = defineAsyncComponent(() => import('/@/components/yhCom/uploadMf.vue'));
const emit = defineEmits(['close', 'getList']);
const props = defineProps({
	info: {
		type: Object,
		default: () => ({}),
	},
});
const customerSourceList = ref([
	{ label: '1688展厅来访', value: '1688展厅来访' },
	{ label: '业务外出', value: '业务外出' },
]);
const picUrl = ref<string[]>([]);
const ruleForm = ref({
	id: null, //主键id
	isConvert: 0, //是否转化
	picUrl: '', //头像
	nickName: null, //昵称
	realName: null, //真实姓名
	status: '跟进', //状态
	sex: null, //性别
	contactPhone: null, //联系电话
	contactEmail: null, //邮箱
	contactWechat: null, //微信
	contactQQ: null, //QQ
	contactOther: null, //其他
	guestType: 0, //来宾类型
	cjName: null, //厂家名
	cjAddr: null, //厂家地址
	hzName: null, //货主名
	sjName: null, //商家名
	sjAddr: null, //商家地址
	intent: null, //意向描述
	lostReason: null, //丢失原因
	remark1: null, //备注1
	remark2: null, //备注2
	remark3: null, //备注3
	guestTimes: null, //来访次数
	firstGuestTime: null, //首次到访时间
	lastGuestTime: null, //末次到访时间
	firstAddTime: null, //首次登记时间
	addUserName: null, //登记人姓名
	addTime: null, //登记时间
	editUserName: null, //修改人姓名
	editTime: null, //修改时间
	hfTimes: null, //回访次数
	firstHfTime: null, //首次回访时间
	firstHfUserName: null, //首次回访人名
	lastHfTime: null, //末次回访时间
	lastHfUserName: null, //末次回访人名
	customerSource: null, //客户来源
});
const rules = reactive<FormRules<typeof ruleForm>>({
	realName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
	contactPhone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
	customerSource: [{ required: true, message: '请选择客户来源', trigger: 'change' }],
});
const refuploadMf = ref<InstanceType<typeof uploadMf> | null>(null);
const ruleFormRef = ref<FormInstance>();
const submitForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	formEl.validate(async (valid) => {
		if (valid) {
			ruleForm.value.picUrl = picUrl.value && picUrl.value?.length ? picUrl.value.join(',') : '';
			const { success } = await AddorEditCrmGuest(ruleForm.value);
			if (success) {
				emit('close');
				emit('getList');
			}
		} else {
			console.log('error submit!');
		}
	});
};
const formatDate = (date: string | null) => {
	return formatters.formatDate(date);
};
onMounted(() => {
	if (props.info) {
		ruleForm.value = JSON.parse(JSON.stringify(props.info));
		if (ruleForm.value.picUrl) {
			picUrl.value = ruleForm.value.picUrl.split(',');
		}
	}
});
</script>
