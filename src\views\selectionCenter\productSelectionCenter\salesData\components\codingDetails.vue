<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange class="publicCss" v-model:startDate="query.startDate" v-model:endDate="query.endDate" style="width: 200px" :clearable="false" />
				<el-select v-model="query.modeType" placeholder="模式" clearable class="publicCss">
					<el-option label="免费入仓" value="免费入仓" />
					<el-option label="自费采购" value="自费采购" />
				</el-select>
				<el-select v-model="query.isXuanPinZhongXin" placeholder="是否为选品中心商品编码" clearable class="publicCss">
					<el-option label="是" value="是" />
					<el-option label="否" value="否" />
				</el-select>
				<el-input v-model.trim="query.styleCode" placeholder="款式编码" class="publicCss" clearable maxlength="50" />
				<el-input v-model.trim="query.goodsCode" placeholder="商品编码" class="publicCss" clearable maxlength="50" />
				<el-select filterable v-model="query.groupId" placeholder="运营组" class="publicCss" clearable>
					<el-option v-for="item in operationsTeamList" :key="item.key" :label="item.value" :value="item.key" />
				</el-select>
				<dataRange
					v-if="isType === 'ID'"
					startPlaceholder="日报开始日期"
					endPlaceholder="日报结束日期"
					class="publicCss"
					v-model:startDate="query.dayReportStartDate"
					v-model:endDate="query.dayReportEndDate"
					style="width: 230px"
				/>
				<el-input v-model.trim="query.proCode" placeholder="ID" class="publicCss" clearable maxlength="50" />
				<div class="pb5">
					<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				height="450"
				id="202506091106"
				:tableCols="tableCols"
				showsummary
				isIndexFixed
				:query="query"
				:query-api="GetSelectionProductCenterSaleDataPlanADetail"
				:export-api="ExportSelectionProductCenterSaleDataPlanADetail"
				:asyncExport="{
					title: `${isType === 'ID' ? 'ID' : '编码'}选品中心销售数据信息明细 ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`,
					isAsync: false,
				}"
			></vxetable>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted, PropType } from 'vue';
import { GetSelectionProductCenterSaleDataPlanADetail, ExportSelectionProductCenterSaleDataPlanADetail } from '/@/api/bookkeeper/selectionProductCenterSaleDataPlanA';
import dayjs from 'dayjs';

interface OptionsProps {
	isType: string | null;
	operationsTeamList: Array<{ key: string | number; value: string }>;
	rowData: Record<string, any>;
	parentData: Record<string, any>;
	fieldName: string;
	isSummary: boolean;
}

const props = defineProps({
	options: {
		type: Object as PropType<OptionsProps>,
		required: true,
	},
});

const { isType, operationsTeamList, rowData, parentData, fieldName, isSummary } = props.options;

const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));

const query = ref({
	startDate: '',
	endDate: '',
	dayReportStartDate: '',
	dayReportEndDate: '',
	modeType: null,
	isXuanPinZhongXin: null,
	styleCode: null,
	goodsCode: null,
	proCode: null,
	groupId: null,
	isXuanPinXiaoShou: null as null | number,
	dataType: parentData.dataType,
});

const table = ref();

const onFormatter = (field: string) => {
	return (row: any) => {
		if (row[field] !== null && row[field] !== undefined) {
			return row[field];
		}
		return 0;
	};
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'yearMonthDayDate', title: '日期', width: 'auto', formatter: 'formatDate' },
	{ sortable: true, field: 'modeType', title: '模式', width: 'auto', align: 'center' },
	{ sortable: true, field: 'styleCode', title: '款式编码', width: 'auto', align: 'center' },
	{
		title: '商品编码',
		align: 'center',
		field: 'goodsCodeCountGrouping',
		children: [
			{ sortable: true, field: 'goodsCode', title: '商品编码', width: 'auto', align: 'center' },
			{ sortable: true, field: 'isXuanPinZhongXin', title: '是否为选品中心商品编码', width: 'auto', align: 'center' },
			{ sortable: true, field: 'groupName', title: '运营组', width: 'auto', align: 'center' },
		],
	},
	{
		title: 'ID',
		align: 'center',
		field: 'IDS',
		children: [
			{ sortable: true, field: 'proCode', title: 'ID', width: 'auto', align: 'center' },
			{ sortable: true, field: 'platForm', title: '平台', width: '70', align: 'center', formatter: 'formatPlatform' },
			{ sortable: true, field: 'productGroupName', title: '运营组', width: 'auto', align: 'center' },
			{ sortable: true, field: 'operateSpecialUserName', title: '运营专员', width: 'auto', align: 'center' },
			{ sortable: true, field: 'userName', title: '运营助理', width: 'auto', align: 'center' },
			{ sortable: true, field: 'saleAmont', title: '销售金额', width: 'auto', align: 'right', formatter: 'fmtAmt2' },
			{ sortable: true, field: 'goodsCount', title: '编码销量', width: 'auto', align: 'right' },
		],
	},
]);

onMounted(() => {
	if (isSummary) {
		query.value.isXuanPinXiaoShou = 1;
	} else if (fieldName === 'onLineIdCount' || fieldName === 'goodsCodeCount' || fieldName === 'styleCodeCount') {
		query.value.isXuanPinXiaoShou = 1;
	} else if (
		fieldName === 'profit6' ||
		fieldName === 'profit6Rate' ||
		fieldName === 'orderCount' ||
		fieldName === 'saleAmount' ||
		fieldName === 'goodsCodeOrderCount' ||
		fieldName === 'goodsCount' ||
		fieldName === 'exitCost' ||
		fieldName === 'warehouseSalary'
	) {
		query.value.isXuanPinXiaoShou = 2;
	} else {
		query.value.isXuanPinXiaoShou = null;
	}

	// 设置查询条件
	query.value.modeType = rowData.modeType || null;

	// 设置日期
	const date = rowData.yearMonthDayDate ? dayjs(rowData.yearMonthDayDate).format('YYYY-MM-DD') : parentData.startDate || dayjs().subtract(30, 'day').format('YYYY-MM-DD');

	query.value.startDate = date;
	query.value.endDate = rowData.yearMonthDayDate ? date : parentData.endDate || dayjs().format('YYYY-MM-DD');

	// 如果类型是ID，添加额外列
	if (isType === 'ID') {
		const profitColumns: VxeTable.Columns[] = [
			{ sortable: true, field: 'profit6', title: '毛六利润(发生)', width: 'auto', align: 'right', formatter: onFormatter('profit6') },
			{ sortable: true, field: 'profit6Rate', title: '毛六利润率(发生)', width: 'auto', align: 'right', formatter: (row: any) => (row.profit6Rate ? (row.profit6Rate * 100).toFixed(2) + '%' : '') },
			{ sortable: true, field: 'orderCount', title: '订单量', width: 'auto', align: 'right', formatter: onFormatter('orderCount') },
			{ sortable: true, field: 'exitCost', title: '出库成本', width: 'auto', align: 'right', formatter: onFormatter('exitCost') },
			{ sortable: true, field: 'warehouseSalary', title: '仓库薪资', width: 'auto', align: 'right', formatter: onFormatter('warehouseSalary') },
		];
		const dailyDate: VxeTable.Columns[] = [
			{
				sortable: true,
				field: 'yearMonthDayDateDayReport',
				title: '日报日期',
				formatter: (row: any) => (row.yearMonthDayDateDayReport ? dayjs(row.yearMonthDayDateDayReport).format('YYYY-MM-DD') : ''),
			},
		];

		const idsColumn = tableCols.value.find((item) => item.field === 'IDS');
		if (idsColumn && idsColumn.children) {
			idsColumn.children = [...dailyDate, ...idsColumn.children, ...profitColumns];
		}
	} else {
		const createDate: VxeTable.Columns[] = [
			{
				sortable: true,
				field: 'createdTime',
				title: '创建时间',
				width: '120',
			},
		];
		const idsColumn = tableCols.value.find((item) => item.field === 'goodsCodeCountGrouping');
		if (idsColumn && idsColumn.children) {
			idsColumn.children = [...idsColumn.children, ...createDate];
		}
	}
});
</script>

<style scoped lang="scss"></style>
