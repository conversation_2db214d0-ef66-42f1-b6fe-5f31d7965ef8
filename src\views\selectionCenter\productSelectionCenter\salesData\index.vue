<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" style="height: 100%; width: 100%">
				<el-tab-pane label="销售数据" name="first" style="height: 100%">
					<codingDimension />
				</el-tab-pane>
				<el-tab-pane label="入仓维度" name="second" style="height: 100%" lazy>
					<warehousingDimension />
				</el-tab-pane>
				<el-tab-pane label="明细" name="third" style="height: 100%" lazy>
					<Details />
				</el-tab-pane>
			</el-tabs>
		</template>
	</Container>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent } from 'vue';
const codingDimension = defineAsyncComponent(() => import('./components/codingDimension.vue'));
const warehousingDimension = defineAsyncComponent(() => import('./components/warehousingDimension.vue'));
const Details = defineAsyncComponent(() => import('./components/details.vue'));
const activeName = ref('first');
</script>
