<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" style="height: 100%; width: 100%">
				<!-- <el-tab-pane label="销售数据" name="first" style="height: 100%">
					<codingDimension />
				</el-tab-pane> -->
				<el-tab-pane label="销售数据" name="second" style="height: 100%" lazy>
					<warehousingDimension @metricClick="handleMetricClick" />
				</el-tab-pane>
				<el-tab-pane label="明细" name="third" style="height: 100%" lazy>
					<Details ref="detailsRef" />
				</el-tab-pane>
			</el-tabs>
		</template>
	</Container>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent, watch } from 'vue';
// const codingDimension = defineAsyncComponent(() => import('./components/codingDimension.vue'));
const warehousingDimension = defineAsyncComponent(() => import('./components/warehousingDimension.vue'));
const Details = defineAsyncComponent(() => import('./components/details.vue'));
const activeName = ref('second');
const detailsRef = ref();

// 存储待执行的搜索参数
const pendingSearchData = ref<{ team: any; type: string } | null>(null);

// 监听detailsRef的变化，当组件加载完成后执行搜索
watch(detailsRef, (newVal) => {
	if (newVal && newVal.onSearch && pendingSearchData.value) {
		console.log(pendingSearchData.value.team, 'team');
		console.log(pendingSearchData.value.type, 'type');
		newVal.onSearch(pendingSearchData.value.team, pendingSearchData.value.type);
		pendingSearchData.value = null; // 清空待执行数据
	}
});

// 处理指标点击事件
const handleMetricClick = async (team: any, type: string) => {
	// 存储搜索参数
	pendingSearchData.value = { team, type };

	// 切换到明细tab
	activeName.value = 'third';

	console.log('切换到明细tab，等待组件加载...');
};
</script>
