<template>
	<Container>
		<template #content>
			<el-tabs v-model="activeName" style="height: 100%; width: 100%">
				<el-tab-pane label="销售数据" name="first" style="height: 100%">
					<codingDimension />
				</el-tab-pane>
				<el-tab-pane label="入仓维度" name="second" style="height: 100%" lazy>
					<warehousingDimension @metricClick="handleMetricClick" />
				</el-tab-pane>
				<el-tab-pane label="明细" name="third" style="height: 100%" lazy>
					<Details ref="detailsRef" />
				</el-tab-pane>
			</el-tabs>
		</template>
	</Container>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent, nextTick } from 'vue';
const codingDimension = defineAsyncComponent(() => import('./components/codingDimension.vue'));
const warehousingDimension = defineAsyncComponent(() => import('./components/warehousingDimension.vue'));
const Details = defineAsyncComponent(() => import('./components/details.vue'));
const activeName = ref('first');
const detailsRef = ref();

// 处理指标点击事件
const handleMetricClick = (team: any, type: string) => {
	// 切换到明细tab
	activeName.value = 'third';

	// 等待组件渲染完成后调用onSearch方法
	nextTick(() => {
		if (detailsRef.value && detailsRef.value.onSearch) {
			detailsRef.value.onSearch(team, type);
		}
	});
};
</script>
