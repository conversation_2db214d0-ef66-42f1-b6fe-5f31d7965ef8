<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange
					class="publicCss"
					v-model:startDate="query.productStartDate"
					v-model:endDate="query.productEndDate"
					style="width: 230px"
					start-placeholder="上新开始时间"
					end-placeholder="上新结束时间"
				/>
				<el-input v-model.trim="query.styleCode" class="publicCss" placeholder="款式编码" clearable maxlength="50" />
				<el-input v-model.trim="query.goodsCode" class="publicCss" placeholder="商品编码" clearable maxlength="50" />
				<el-input v-model.trim="query.goodsName" class="publicCss" placeholder="商品名称" clearable maxlength="50" />
				<el-select
					v-model="query.buyChoiceList"
					placeholder="采购"
					class="publicCss"
					style="width: 170px"
					:filter-method="(value: any) => buyChoiceMethod(value)"
					clearable
					filterable
					multiple
					collapse-tags
					collapse-tags-tooltip
					:reserve-keyword="false"
				>
					<el-option v-for="item in brandOption" :key="item.value" :label="item.label" :value="item.label"> </el-option>
				</el-select>
				<el-select
					clearable
					@clear="handleClear"
					v-model="query.mainBusinessCategoriesDetailIdList"
					multiple
					filterable
					remote
					reserve-keyword
					placeholder="请输入主类目关键词"
					remote-show-suffix
					collapse-tags
					:multiple-limit="2"
					:remote-method="remoteMethod"
					:max-collapse-tags="2"
					:loading="selloading"
					style="width: 800px"
				>
					<el-option v-for="item in selList" :key="item.value" :label="item.label" :value="normalizeJson(item.value)" />
				</el-select>
				<el-select v-model="query.isEnabled" placeholder="状态" class="publicCss" clearable filterable>
					<el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<el-input v-model.trim="query.laiPuHuoProCode" class="publicCss" placeholder="来铺货Id" clearable maxlength="50" />
				<el-input v-model.trim="query.remark" class="publicCss" placeholder="备注" clearable maxlength="50" />
				<div class="pb5">
					<el-button type="primary" @click="getList">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="202506281110" :keyField="'goodscode'" :is-asc="false" :tableCols="tableCols" showsummary isIndexFixed :query="query" :query-api="PageProductSellSituatioLitsAsync">
				<template #toolbar_buttons>
					<el-button @click="exportProps" type="primary" :disabled="isExport">导出</el-button>
				</template>
			</vxetable>
			<el-dialog v-model="editVisible" title="编辑" width="450" draggable overflow style="margin-top: -18vh !important" @close="handleClose" :close-on-click-modal="false">
				<div style="padding-top: 10px" v-loading="listLoading">
					<el-form :model="singleform" :rules="singlerules" ref="ruleFormRef">
						<el-form-item label="来铺货Id" :label-width="'100px'" prop="laiPuHuoProCode">
							<el-input v-model.trim="singleform.laiPuHuoProCode" class="publicCss" placeholder="来铺货Id" clearable maxlength="50" />
						</el-form-item>
						<el-form-item label="备注" :label-width="'100px'" prop="remark">
							<el-input
								v-model.trim="singleform.remark"
								maxlength="100"
								style="width: 100%"
								placeholder="请输入备注"
								clearable
								:autosize="{ minRows: 4, maxRows: 4 }"
								show-word-limit
								type="textarea"
								resize="none"
							/>
						</el-form-item>
					</el-form>
				</div>
				<template #footer>
					<div style="display: flex; justify-content: center; align-items: center; gap: 20px">
						<el-button @click="editVisible = false">取消</el-button>
						<el-button type="primary" @click="onSingleSave(ruleFormRef)" :disabled="listLoading">确定</el-button>
					</div>
				</template>
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted } from 'vue';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
import { GetCrmLeiMuList } from '/@/api/customerservice/customerGroup';
import { PageProductSellSituatioLitsAsync, ExportProductListAsync, EditProductSellSituation } from '/@/api/operatemanage/productSellSituation';
import { GetAllBianMaBrandAsync } from '/@/api/inventory/warehouse';
import { debounce } from 'lodash-es';
import { ElMessage, FormInstance, ElMessageBox } from 'element-plus';
const brandList = ref<any[]>([]);
const brandOption = ref<any[]>([]);
const query = ref({
	productStartDate: dayjs().format('YYYY-MM-DD'), //上新开始时间
	productEndDate: dayjs().format('YYYY-MM-DD'), //上新结束时间
	styleCode: '', //款式编码
	goodsCode: '', //商品编码
	goodsName: '', //商品名称
	buyChoiceList: [], //采购
	isEnabled: '', //状态
	laiPuHuoProCode: '', //来铺货Id
	remark: '', //备注
});
const table = ref();
const isExport = ref(false);
const statusList = ref([
	{ label: '备用', value: 0 },
	{ label: '启用', value: 1 },
	{ label: '禁用', value: -1 },
]);
const ruleFormRef = ref();
const editVisible = ref(false);
const listLoading = ref(false);
const singleform = ref<{
	goodsCode: string | number;
	laiPuHuoProCode: string;
	remark: string;
}>({
	goodsCode: '',
	laiPuHuoProCode: '',
	remark: '',
});

const normalizeJson = (jsonStr: string) => {
	if (!jsonStr) return '';
	return JSON.stringify(JSON.parse(jsonStr), Object.keys(JSON.parse(jsonStr)).sort());
};

const remoteMethod = async (query: string) => {
	console.log(query); // 输入的关键字
	if (!query) return;
	selloading.value = true;
	const { data } = await GetCrmLeiMuList({ keyWords: query });
	let label1 = '';
	let newarr = [];
	data.list.map((item: any) => {
		label1 = item.businessCategory + (item.categoryName1 ? '-' + item.categoryName1 : '') + (item.categoryName2 ? '-' + item.categoryName2 : '');
		newarr.push({ label: label1, value: normalizeJson(JSON.stringify(item)) });
	});

	newarr.push(...selListold.value);

	selList.value = newarr;

	selloading.value = false;
};

const handleClear = () => {
	ruleForm.value.mainBusinessCategoriesDetailList = [];
	console.log(ruleForm.value.mainBusinessCategoriesDetailList);
};
const singlerules = ref({
	laiPuHuoProCode: [{ required: true, message: '请输入来铺货Id', trigger: 'blur' }],
	remark: [{ required: true, message: '请输入备注', trigger: 'blur' }],
});
const getList = ()=>{
  
}
const buyChoiceMethod = (value: any) => {
	let a = brandList.value;
	if (value !== '') {
		brandOption.value = a.filter((item: any) => item.label.includes(value));
	} else {
		brandOption.value = a;
	}
};
const exportProps = () => {
	isExport.value = true;
	const params = { ...table.value.query, ...query.value };
	ExportProductListAsync(params)
		.then((data: any) => {
			if (data) {
				const aLink = document.createElement('a');
				let blob = new Blob([data], { type: 'application/vnd.ms-excel' });
				aLink.href = URL.createObjectURL(blob);
				aLink.setAttribute('download', '1688选品中心产品推新' + new Date().toLocaleString() + '.xlsx');
				aLink.click();
				isExport.value = false;
			}
		})
		.catch(() => {
			isExport.value = false;
		});
};
const getList = () => {
	table.value.query.currentPage = 1;
	table.value.refreshTable(true);
};
const onSingleSave = debounce(async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate(async (valid, fields) => {
		if (valid) {
			listLoading.value = true;
			const { success } = await EditProductSellSituation(singleform.value);
			if (success) {
				ElMessage.success('编辑成功');
				editVisible.value = false;
				getList();
			}
			listLoading.value = false;
		}
	});
}, 500);

const handleClose = () => {
	editVisible.value = false;
	ruleFormRef.value.resetFields();
};
const handleEdit = (row: any) => {
	editVisible.value = true;
	singleform.value.laiPuHuoProCode = row.laiPuHuoProCode;
	singleform.value.remark = row.remark;
	singleform.value.goodsCode = row.goodsCode;
};
const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'productCreateDate', title: '上新日期', width: '100', formatter: 'formatDate' },
	{ sortable: true, field: 'productCreateDays', title: '上新天数', width: '100' },
	{ field: 'productImg', title: '图片', type: 'image', width: '70' },
	{ sortable: true, field: 'styleCode', title: '款式编码', width: '150' },
	{ field: 'businessCategory', title: '主营类目', width: '100' },
	{ field: 'categoryName1', title: '一级类目名称', width: '100' },
	{ field: 'categoryName2', title: '二级类目名称', width: '100' },
	{ sortable: true, field: 'goodsCode', title: '商品编码', width: '150' },
	{ sortable: true, field: 'goodsName', title: '商品名称', width: '150' },
	{ sortable: true, field: 'isEnabled', title: '状态', width: '100', formatter: (row: any) => statusList.value.find((item: any) => item.value === row.isEnabled)?.label },
	{
		sortable: true,
		field: 'providerLink',
		title: '供应链链接',
		width: '100',
		type: 'html',
		permissions: 'SupplyChainLinkPermission',
		formatter: (row: any) => (row.providerLink ? `<a href="${row.providerLink}" target="_blank">${row.providerLink}</a>` : ''),
	},
	{ sortable: true, field: 'costPrice', title: '成本价', width: '100', align: 'right' },
	{ sortable: true, field: 'remainNumber', title: '库存数量', width: '100', align: 'right' },
	{ sortable: true, field: 'brand', title: '采购', width: '100' },
	{ sortable: true, field: 'pinDuoDuo', title: '拼多多', width: '100', align: 'right' },
	{ sortable: true, field: 'douYin', title: '抖音', width: '100', align: 'right' },
	{ sortable: true, field: 'taoBao', title: '淘宝', width: '100', align: 'right' },
	{ sortable: true, field: 'kuaiShou', title: '快手', width: '100', align: 'right' },
	{ sortable: true, field: 'shiPinHao', title: '视频号', width: '100', align: 'right' },
	{ sortable: true, field: 'aLiBaBa', title: '阿里巴巴', width: '100', align: 'right' },
	{ sortable: true, field: 'jingDong', title: '京东', width: '100', align: 'right' },
	{ sortable: true, field: 'fengXiao', title: '分销', width: '100', align: 'right' },
	{ sortable: true, field: 'laiPuHuoProCode', title: '来铺货Id', width: '100' },
	{ sortable: true, field: 'remark', title: '备注', width: '100' },
	{
		title: '操作',
		align: 'center',
		width: '80',
		type: 'btnList',
		minWidth: '80',
		field: 'operation',
		btnList: [{ title: '编辑', handle: handleEdit, permissions: 'ProductLaunchColumnEditor' }],
		fixed: 'right',
	},
]);
onMounted(async () => {
	const res = await GetAllBianMaBrandAsync({});
	if (!res?.success) return;
	brandList.value = res.data.map((item: any) => ({
		value: item.key,
		label: item.value,
	}));
});
</script>

<style scoped lang="scss"></style>
