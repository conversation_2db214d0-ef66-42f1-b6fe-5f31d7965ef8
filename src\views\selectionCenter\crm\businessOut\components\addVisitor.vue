<template>
	<el-form ref="ruleFormRef" :model="ruleForm" status-icon label-width="120" class="demo-ruleForm" :rules="rules">
		<el-row :gutter="10" class="mb20">
			<el-col :span="6">
				<el-form-item label="厂家名称:" prop="cjName">
					<el-input v-model="ruleForm.cjName" placeholder="厂家名称" clearable maxlength="50" />
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="厂家姓名:" prop="realName">
					<el-input class="w100" v-model="ruleForm.realName" placeholder="厂家姓名" clearable maxlength="50" />
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="联系电话:" prop="contactPhone">
					<el-input v-model="ruleForm.contactPhone" placeholder="联系电话" clearable maxlength="50" />
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="厂家地址:" prop="cjAddr">
					<el-input v-model="ruleForm.cjAddr" placeholder="厂家地址" clearable maxlength="50" />
				</el-form-item>
			</el-col>
		</el-row>
		<el-row :gutter="10" class="mb20">
			<el-col :span="6">
				<el-form-item label="拜访日期:">
					<dataRange class="publicCss" v-model:date="ruleForm.lastHfTime" :clearable="false" type="date" />
				</el-form-item>
			</el-col>
			<!-- <el-col :span="6">
				<el-form-item label="主营类目:" prop="mainBusinessCategories">
					<el-input v-model="ruleForm.mainBusinessCategories" placeholder="主营类目" clearable maxlength="50"
						resize="none" />
				</el-form-item>
			</el-col> -->

			<el-col :span="6">
				<el-form-item label="状态:" prop="status">
					<el-select v-model="ruleForm.status" placeholder="状态" style="width: 240px" @change="ruleForm.status != '丢失' ? (ruleForm.lostReason = null) : ''">
						<el-option label="跟进" value="跟进" />
						<el-option label="丢失" value="丢失" />
						<el-option label="已拉群" value="已拉群" />
						<el-option label="已入仓" value="已入仓" />
					</el-select>
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="丢失原因:">
					<el-input type="textarea" row="3" v-model="ruleForm.lostReason" placeholder="丢失原因" clearable maxlength="50" :disabled="ruleForm.status != '丢失'" resize="none" />
				</el-form-item>
			</el-col>
		</el-row>
		<el-row :gutter="10" class="mb20">
			<el-col :span="24">
				<el-form-item label="类目描述:">
					<el-select
						clearable
						@clear="handleClear"
						v-model="ruleForm.mainBusinessCategoriesDetailList"
						multiple
						filterable
						remote
						reserve-keyword
						placeholder="请输入主类目关键词"
						remote-show-suffix
						collapse-tags
						:multiple-limit="2"
						:remote-method="remoteMethod"
						:max-collapse-tags="2"
						:loading="selloading"
						style="width: 800px"
					>
						<el-option v-for="item in selList" :key="item.value" :label="item.label" :value="normalizeJson(item.value)" />
					</el-select>
				</el-form-item>
			</el-col>
		</el-row>
		<el-row :gutter="10" class="mb20">
			<el-col :span="6">
				<el-form-item label="与厂家沟通内容:" prop="yuChangJiaGouTongNeiRong">
					<el-input type="textarea" row="3" v-model="ruleForm.yuChangJiaGouTongNeiRong" placeholder="与厂家沟通内容" clearable maxlength="200" resize="none" />
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="厂家描述:" prop="intent">
					<el-input type="textarea" row="3" v-model="ruleForm.intent" placeholder="厂家描述" clearable maxlength="200" resize="none" />
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="计划回访日期:">
					<dataRange class="publicCss" v-model:date="ruleForm.jiHuaHfTime" :clearable="false" type="date" />
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="计划回访沟通内容:">
					<el-input type="textarea" row="3" v-model="ruleForm.jiHuaHuiFangGouTongNeiRong" placeholder="计划回访沟通内容" clearable maxlength="200" resize="none" />
				</el-form-item>
			</el-col>
		</el-row>

		<el-row :gutter="10" class="mb20">
			<el-col :span="6">
				<el-form-item label="业务员:">
					<userSelect style="width: 220px" v-model:value="ruleForm.jieDaiUserId" :isDdUserId="false" v-model:label="ruleForm.jieDaiUserName" placeholder="请输入业务员姓名" />
				</el-form-item>
			</el-col>
			<el-col :span="6">
				<el-form-item label="入库单号:" :prop="ruleForm.status == '已入仓' ? 'warehousingNo' : ''">
					<el-input v-model="ruleForm.warehousingNo" placeholder="请输入入库单号" clearable maxlength="200" />
				</el-form-item>
			</el-col>
			<el-col :span="12">
				<el-form-item label="附件:">
					<uploadMf
						v-model:imagesStr="ruleForm.attachmentFileList"
						uploadFormat=".xlsx,.xls,.XLSX,.XLS"
						ref="uploadMfRef"
						:upstyle="{ height: 40, width: 40 }"
						:limit="1"
						uploadName="上传附件"
						:isImage="false"
					/>
				</el-form-item>
			</el-col>
		</el-row>
		<div class="mt10">
			<div style="display: flex; justify-content: center; align-items: center">
				<el-button @click="emit('close')">取消</el-button>
				<el-button type="primary" @click="submitForm(ruleFormRef)">保存</el-button>
			</div>
		</div>
	</el-form>
</template>

<script lang="ts" setup>
import { reactive, ref, defineAsyncComponent, onMounted } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { AddorEditCrmGuest, GetCrmLeiMuList } from '/@/api/customerservice/customerGroup';
import { formatters } from '/@/utils/vxetableFormats';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const uploadMf = defineAsyncComponent(() => import('/@/components/yhCom/uploadMf.vue'));
const userSelect = defineAsyncComponent(() => import('/@/components/yhCom/userSelect.vue'));
const emit = defineEmits(['close', 'getList']);
const props = defineProps({
	info: {
		type: Object,
		default: () => ({}),
	},
});
const uploadMfRef = ref();
const picUrl = ref<string[]>([]);
const selloading = ref(false);
const selList = ref<any[]>([]);
const selListold = ref<any[]>([]);

const ruleForm = ref({
	id: null, //主键id
	cjName: null, //厂家名
	cjAddr: null, //厂家地址
	realName: null, //真实姓名
	contactPhone: null, //联系电话
	lastHfTime: null, //末次回访时间
	mainBusinessCategories: '', //主营类目
	status: '跟进', //状态
	lostReason: null, //丢失原因
	yuChangJiaGouTongNeiRong: '', //与厂家沟通内容
	intent: null, //意向描述
	jiHuaHfTime: null, //计划回访时间
	jieDaiUserId: null, //接待人ID
	jieDaiUserName: '', //接待人姓名
	attachmentFileList: [] as string[], //附件列表
	attachmentFile: '', //附件
	jiHuaHuiFangGouTongNeiRong: '', //计划回访沟通内容
	customerSource: '业务外出', //客户来源
	mainBusinessCategoriesDetailList: [] as string[], //主营一二级类目详情
	warehousingNo: '', //入库单号
});
const rules = reactive<FormRules<typeof ruleForm>>({
	cjName: [{ required: true, message: '请输入厂家名称', trigger: 'blur' }],
	cjAddr: [{ required: true, message: '请输入厂家地址', trigger: 'blur' }],
	contactPhone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
	status: [{ required: true, message: '请选择状态', trigger: 'change' }],
	intent: [{ required: true, message: '请输入厂家描述', trigger: 'blur' }],
	// mainBusinessCategories: [{ required: true, message: '请输入主营类目', trigger: 'blur' }],
	mainBusinessCategoriesDetailList: [{ required: true, message: '请选择主营类目', trigger: 'blur' }],
	yuChangJiaGouTongNeiRong: [{ required: true, message: '请输入与厂家沟通内容', trigger: 'blur' }],
	jiHuaHuiFangGouTongNeiRong: [{ required: true, message: '请输入计划回访沟通内容', trigger: 'blur' }],
	realName: [{ required: true, message: '请输入厂家姓名', trigger: 'blur' }],
	customerSource: [{ required: true, message: '请选择客户来源', trigger: 'change' }],
	warehousingNo: [{ required: true, message: '请输入入库单号', trigger: 'blur' }],
});
const refuploadMf = ref<InstanceType<typeof uploadMf> | null>(null);
const ruleFormRef = ref<FormInstance>();
const submitForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	formEl.validate(async (valid): Promise<any> => {
		if (valid) {
			if (ruleForm.value.status == '已入仓' && !ruleForm.value.warehousingNo) return window.$message.error('状态为已入仓时,入库单号必填');
			ruleForm.value.attachmentFile = uploadMfRef.value.fileList && uploadMfRef.value.fileList?.length > 0 ? JSON.stringify(uploadMfRef.value.fileList) : '';
			let params = ruleForm.value;
			let newarr = <any>[];
			ruleForm.value.mainBusinessCategoriesDetailList.forEach((item: any, i: number) => {
				if (item && typeof item == 'string') {
					newarr.push(JSON.parse(item));
				}
			});
			params.mainBusinessCategoriesDetailList = newarr;
			const { success } = await AddorEditCrmGuest(params);
			if (success) {
				emit('close');
				emit('getList');
			}
		} else {
			console.log('error submit!');
		}
	});
};

const normalizeJson = (jsonStr: string) => {
	if (!jsonStr) return '';
	return JSON.stringify(JSON.parse(jsonStr), Object.keys(JSON.parse(jsonStr)).sort());
};

const remoteMethod = async (query: string) => {
	console.log(query); // 输入的关键字
	if (!query) return;
	selloading.value = true;
	const { data } = await GetCrmLeiMuList({ keyWords: query });
	let label1 = '';
	let newarr = [];
	data.list.map((item: any) => {
		label1 = item.businessCategory + (item.categoryName1 ? '-' + item.categoryName1 : '') + (item.categoryName2 ? '-' + item.categoryName2 : '');
		newarr.push({ label: label1, value: normalizeJson(JSON.stringify(item)) });
	});

	newarr.push(...selListold.value);

	selList.value = newarr;

	selloading.value = false;
};

const handleClear = () => {
	ruleForm.value.mainBusinessCategoriesDetailList = [];
	console.log(ruleForm.value.mainBusinessCategoriesDetailList);
};

onMounted(() => {
	if (props.info) {
		ruleForm.value = JSON.parse(JSON.stringify(props.info));
		ruleForm.value.mainBusinessCategoriesDetailList = [];
		let newarr = JSON.parse(JSON.stringify(props.info.mainBusinessCategoriesDetailList));
		newarr.map((item: any) => {
			let items = normalizeJson(JSON.stringify(item));
			ruleForm.value.mainBusinessCategoriesDetailList.push(items);
		});
		//深度拷贝

		// newarr.map((item: any) => {
		//     item = JSON.stringify(item);
		// })
		// console.log("newarr",newarr);
		let newarr2 = <any[]>[];
		newarr.map((item: any) =>
			newarr2.push({
				label: item.businessCategory + (item.categoryName1 ? '-' + item.categoryName1 : '') + (item.categoryName2 ? '-' + item.categoryName2 : ''),
				value: JSON.stringify(item),
			})
		);

		selList.value = JSON.parse(JSON.stringify(newarr2));
		selListold.value = JSON.parse(JSON.stringify(newarr2));

		if (ruleForm.value.attachmentFile) {
			ruleForm.value.attachmentFileList = ruleForm.value.attachmentFile ? [JSON.parse(ruleForm.value.attachmentFile)[0].url] : [];
		}
	}
});
</script>
