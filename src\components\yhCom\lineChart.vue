<template>
	<div style="height: 100%; width: 100%; padding-left: 10px; overflow: auto">
		<div v-if="chartData && chartData.series && chartData.series != null && chartData.series.length > 0" :id="'lineChart' + random" :style="props.thisStyle"></div>
		<div v-else>没有可展示的图表!</div>
	</div>
</template>

<script setup lang="ts" name="">
import { ref, onMounted, defineProps, nextTick, defineExpose } from 'vue';
import * as echarts from 'echarts';
const random = ref('');
const props = defineProps({
	thisStyle: {
		type: Object,
		default: function () {
			return {
				width: '100%',
				height: '550px',
				'box-sizing': 'border-box',
				'line-height': '360px',
			};
		},
	},
	chartData: {
		type: Object,
		default: () => {
			return {};
		},
	},
	gridStyle: {
		type: Object,
		default: function () {
			return {
				top: '20%',
				left: '10%',
				right: '4%',
				bottom: '5%',
				containLabel: false,
			};
		},
	},
	tooltipFormatter: {
		type: Function,
		default: null,
	},
});
const chatProps = ref({
	legend: [],
	xAxis: [],
	series: [],
	title: '',
});
const option = ref({});
const getCharts = (val: any) => {
	option.value = {
		title: {
			text: val.title ? val.title : '',
		},
		tooltip: {
			trigger: 'axis',
			formatter: props.tooltipFormatter || undefined,
		},
		legend: {
			top: 30,
			data: val.legend || [],
		},
		toolbox: {
			show: true,
			feature: {
				magicType: { type: ['line', 'bar'] },
				restore: {},
				saveAsImage: {},
			},
		},
		xAxis: {
			type: 'category',
			data: val.xAxis || [],
		},
		yAxis:
			val.yAxis && val.yAxis.length
				? val.yAxis.map((axis: any) => ({
						...axis,
						type: 'value',
						axisLabel: {
							formatter: (value: number) => {
								if (axis.name?.includes('百分比')) {
									return `${value}%`;
								}
								const absValue = Math.abs(value);
								if (absValue >= 10000) {
									return `${(value / 10000).toFixed(1)}万`;
								}
								return value;
							},
						},
						splitLine: {
							show: true,
							lineStyle: {
								type: 'dashed',
							},
						},
					}))
				: {
						type: 'value',
						axisLabel: {
							formatter: function (value: number) {
								const absValue = Math.abs(value);
								if (absValue >= 10000) {
									return (value / 10000).toFixed(1) + '万';
								}
								return value;
							},
						},
						splitLine: {
							show: true,
							lineStyle: {
								type: 'dashed',
							},
						},
					},
		grid: props.gridStyle,
		series: val.series || [],
	};
};
const reSetChart = (val: any) => {
	const chartDom = document.getElementById('lineChart' + random.value);
	const myChart = echarts.init(chartDom);
	myChart && myChart.dispose();
	getCharts(val);
	createChart();
};
const createChart = () => {
	const chartDom = document.getElementById('lineChart' + random.value);
	const myChart = echarts.init(chartDom);
	option.value && myChart.setOption(option.value);
	window.addEventListener('resize', () => {
		myChart.resize();
	});
};
onMounted(() => {
	var e = 10;
	var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
		a = t.length,
		n = '';
	for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
	random.value = n;
	nextTick(() => {
		chatProps.value = JSON.parse(JSON.stringify(props.chartData));
		getCharts(chatProps.value);
		createChart();
	});
});
defineExpose({
	reSetChart,
});
</script>

<style scoped lang="scss"></style>
