<template>
	<div class="settlement-details" v-loading="loadingList">
		<!-- 基本信息描述 -->
		<el-descriptions title="结算单信息" :column="2" border class="settlement-info">
			<el-descriptions-item label="结算单号">{{ listInfo.id }}</el-descriptions-item>
			<el-descriptions-item label="结算时间">{{ listInfo.createdTime }}</el-descriptions-item>
			<el-descriptions-item label="结算人">{{ listInfo.createdUserName }}</el-descriptions-item>
			<el-descriptions-item label="结算总金额">{{ listInfo.balanceAmount }}</el-descriptions-item>
			<el-descriptions-item label="结算总数量">{{ listInfo.balanceCount }}</el-descriptions-item>
		</el-descriptions>

		<!-- 采购单信息标题 -->
		<div class="section-title">采购单信息</div>

		<!-- 采购单信息表格 -->
		<vxetable
			ref="table"
			id="20250613094053"
			height="330"
			:tableCols="tableCols"
			:data="listInfo.balanceRecordDtlList || []"
			:remote="false"
			showsummary
			isIndexFixed
			:query="query"
			:isNeedPager="false"
			:isNeedQueryApi="false"
		></vxetable>
		<div class="form-buttons">
			<el-button @click="handleCancel">取消</el-button>
			<!-- <el-button type="primary">确定</el-button> -->
		</div>
	</div>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted, defineEmits } from 'vue';
import { GetCwDelayPaySupplierBalanceRecord } from '/@/api/cwManager/cwDelayPay';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));

const props = defineProps({
	data: {
		type: Object,
		default: () => {},
	},
});
const emit = defineEmits(['handleCancel']);
const loadingList = ref(false);
const listInfo = ref({
	balanceRecordDtlList: [], // 结算单明细列表
	id: '', // 结算单号
	createdTime: '', // 结算时间
	createdUserName: '', // 结算人
	balanceAmount: '', // 结算总金额
	balanceCount: '', // 结算总数量
});

// 查询参数
const query = ref({
	balanceIds: null,
});

const table = ref();

// 表格列配置
const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'supplierName', title: '供应商名称', align: 'center' },
	{ sortable: true, field: 'goodsCode', title: '商品编码', align: 'center' },
	{ sortable: true, field: 'balanceCount', title: '结算数量', formatter: 'fmtAmt0', align: 'right' },
	{ sortable: true, field: 'buyNo', title: '对应采购单', align: 'center' },
	{ sortable: true, field: 'purchaseCount', title: '采购数量', formatter: 'fmtAmt0', align: 'right' },
	{ sortable: true, field: 'inWareCount', title: '入库数量', formatter: 'fmtAmt0', align: 'right' },
	{ sortable: true, field: 'cost', title: '成本价', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'balanceAmount', title: '结算金额', formatter: 'fmtAmt2', align: 'right' },
]);

onMounted(async () => {
	query.value.balanceIds = props.data.id;
	loadingList.value = true;
	const res = await GetCwDelayPaySupplierBalanceRecord(query.value);
	listInfo.value = res.data;
	table.value.onAssignedData(listInfo.value.balanceRecordDtlList);
	loadingList.value = false;
});

const handleCancel = () => {
	emit('handleCancel');
};
</script>

<style scoped lang="scss">
.settlement-details {
	padding: 20px;
	background-color: #fff;

	.settlement-info {
		margin-bottom: 10px;

		:deep(.el-descriptions__title) {
			font-size: 16px;
			font-weight: 600;
			color: #333;
			margin-bottom: 16px;
		}

		:deep(.el-descriptions__body) {
			.el-descriptions__table {
				.el-descriptions__cell {
					padding: 12px 16px;

					&.is-bordered-label {
						background-color: #fafafa;
						font-weight: 500;
						color: #666;
					}
				}
			}
		}
	}

	.section-title {
		font-size: 14px;
		font-weight: 600;
		color: #333;
		margin-top: 20px;
		padding-left: 8px;
		border-left: 3px solid #409eff;
	}
}

.form-buttons {
	display: flex;
	justify-content: end;
	align-items: center;
	gap: 20px;

	.el-button {
		min-width: 80px;
		height: 36px;
		border-radius: 4px;
		font-weight: 500;
		margin-top: 20px;
	}
}
</style>
