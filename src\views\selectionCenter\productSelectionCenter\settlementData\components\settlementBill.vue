<template>
	<Container>
		<template #header>
			<div class="topCss">
				<div class="publicCss">
					<manyInput v-model:inputt="query.supplierNames" :title="'供应商名称'" :verifyNumber="false" :placeholder="'请输入供应商名称'" :maxRows="100" :maxlength="1000" />
				</div>
				<dataRange
					class="publicCss"
					v-model:startDate="query.balanceTimeStart"
					v-model:endDate="query.balanceTimeEnd"
					:startPlaceholder="'结算开始时间'"
					:endPlaceholder="'结算结束时间'"
					style="width: 230px"
				/>
				<el-input v-model.trim="query.balanceNames" class="publicCss" placeholder="结算人" clearable maxlength="50" />
				<el-select v-model="query.balanceOrderStatus" placeholder="状态" class="publicCss" clearable filterable multiple collapse-tags collapse-tags-tooltip>
					<el-option v-for="item in statuslist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select v-model="query.sources" placeholder="来源" class="publicCss" clearable filterable multiple collapse-tags collapse-tags-tooltip>
					<el-option v-for="item in sourcelist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<div class="pb5">
					<el-button type="primary" @click="getList">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				id="202506251313"
				:tableCols="tableCols"
				showsummary
				isIndexFixed
				:query="query"
				isNeedCheckBox
				@select="onCheckBoxMethod"
				:query-api="GetCwDelayPaySupplierBalanceRecordList"
				:export-api="ExportCwDelayPaySupplierBalanceRecord"
				:asyncExport="{
					title: `结算单导出数据 ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`,
					isAsync: true,
				}"
			>
				<template #toolbar_buttons>
					<el-button @click="onConfigurationMethod" type="primary">配置</el-button>
					<el-button @click="onApprovalMethod" type="primary">发起审批</el-button>
					<el-button @click="onDeleteMethod" type="primary">作废</el-button>
				</template>
			</vxetable>
			<el-dialog v-model="detailInfo.visible" width="60%" draggable overflow title="结算单明细">
				<settlementDetails v-if="detailInfo.visible" :data="detailInfo.data" @handleCancel="detailInfo.visible = false" />
			</el-dialog>
			<el-dialog v-model="configurationVisible" width="40%" draggable overflow title="配置">
				<div class="configuration-content">
					<div class="config-section">
						<el-checkbox v-model="configurationInfo.thirtyDayNoSaleMark" class="main-checkbox bottomMargin" @change="handleThirtyDayNoSaleMarkChange"> 30天无动销 </el-checkbox>
						<div class="sub-options">
							<div class="input-group">
								<el-checkbox
									v-model="configurationInfo.salesRatioMark"
									:disabled="!configurationInfo.thirtyDayNoSaleMark"
									class="main-checkbox round-checkbox"
									@change="handleSalesRatioChange($event, '销量比例')"
								>
									销量比例小于等于
								</el-checkbox>
								<el-input-number
									:controls="false"
									v-model="configurationInfo.salesRatioValue"
									:max="100"
									:min="0"
									controls-position="right"
									:precision="2"
									placeholder="请输入数值"
									style="width: 120px; margin: 0 5px"
									:disabled="!configurationInfo.thirtyDayNoSaleMark"
								/>%
							</div>
							<div class="input-group">
								<el-checkbox
									v-model="configurationInfo.salesMark"
									:disabled="!configurationInfo.thirtyDayNoSaleMark"
									class="main-checkbox round-checkbox"
									@change="handleSalesRatioChange($event, '销量')"
								>
									销量小于等于
								</el-checkbox>
								<el-input-number
									:controls="false"
									v-model="configurationInfo.salesValue"
									:max="999999"
									:min="0"
									controls-position="right"
									:precision="0"
									placeholder="请输入数值"
									style="width: 120px; margin: 0 5px"
									:disabled="!configurationInfo.thirtyDayNoSaleMark"
								/>
							</div>
						</div>
					</div>
					<div class="config-section">
						<el-checkbox v-model="configurationInfo.fifteenDayNoInWareMark" class="config-checkbox"> 15天内商品编码无新入库单，自动生成上年采购单结算 </el-checkbox>
					</div>
					<div class="config-section">
						<el-checkbox v-model="configurationInfo.createPurchaseCombineBalanceMark" class="config-checkbox"> 生成新采购单自动生成对应商品编码结算单 </el-checkbox>
					</div>
					<div class="dialog-footer">
						<el-button @click="cancelConfiguration" class="cancel-btn">取消</el-button>
						<el-button type="primary" @click="confirmConfiguration" class="confirm-btn">确定</el-button>
					</div>
				</div>
			</el-dialog>
			<el-dialog v-model="approvalVisible" width="50%" draggable overflow title="审批" @close="approvalClose">
				<initiateApproval v-if="approvalVisible" :checkBoxList="checkBoxList" @reloadTable="getList" @closeApproval="approvalVisible = false" />
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent } from 'vue';
import {
	GetCwDelayPaySupplierBalanceRecordList,
	ExportCwDelayPaySupplierBalanceRecord,
	GetCwDelayPaySupplierBalanceConfigAsync,
	AddOrUpdateCwDelayPaySupplierBalanceConfigAsync,
	CancelCwDelayPayBalance,
} from '/@/api/cwManager/cwDelayPay';
import dayjs from 'dayjs';
import { ElMessage, ElMessageBox } from 'element-plus';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const manyInput = defineAsyncComponent(() => import('/@/components/yhCom/manyInput.vue'));
const settlementDetails = defineAsyncComponent(() => import('./settlementDetails.vue'));
const initiateApproval = defineAsyncComponent(() => import('./initiateApproval.vue'));
const query = ref({
	supplierNames: '', // 供应商名称
	balanceTimeStart: dayjs().subtract(30, 'day').format('YYYY-MM-DD'), // 结算开始时间
	balanceTimeEnd: dayjs().format('YYYY-MM-DD'), // 结算结束时间
	balanceNames: '', // 结算人
	balanceOrderStatus: [], // 状态
	sources: [], // 来源
});
const detailInfo = ref({
	visible: false,
	data: {},
});
const configurationVisible = ref(false);
const approvalVisible = ref(false);

// 定义选中行的简易类型（只关心校验所需字段）
interface BalanceRow {
	instanceId?: string | null;
	supplierId?: string | null;
	[key: string]: any;
}

// 勾选的结算单列表
const checkBoxList = ref<BalanceRow[]>([]);
const defaultCfg = {
	thirtyDayNoSaleMark: false,
	salesRatioValue: undefined,
	salesValue: undefined,
	fifteenDayNoInWareMark: false,
	createPurchaseCombineBalanceMark: false,
	salesRatioMark: false,
	salesMark: false,
};
const configurationInfo = ref(defaultCfg);
const statuslist = ref([
	{ label: '待审核', value: '待审核' },
	{ label: '审核中', value: '审核中' },
	{ label: '审核通过', value: '审核通过' },
	{ label: '审核拒绝', value: '审核拒绝' },
	{ label: '作废', value: '作废' },
]);
const sourcelist = ref([
	{ label: '系统', value: '系统' },
	{ label: '人工', value: '人工' },
]);
const table = ref();

const onCheckBoxMethod = (val: BalanceRow[]) => {
	checkBoxList.value = val;
};

const getList = () => {
	table.value.refreshTable(true);
	approvalClose();
};

const approvalClose = () => {
	checkBoxList.value = [];
	table.value.clearSelection();
};

const onDeleteMethod = () => {
	const list = checkBoxList.value;
	if (!list.length) return ElMessage.warning('请选择要作废的结算单');
	if (list.length > 10) return ElMessage.warning('最多只能勾选10条结算单');
	if (list.length === 1) {
		if (!list[0].id) return ElMessage.warning('单条结算单的 id 不能为空');
	}
	ElMessageBox.confirm('是否确认作废?', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		const { data, success } = await CancelCwDelayPayBalance(list.map((i) => i.id));
		if (success) {
			ElMessage.success(data || '作废成功');
			getList();
		}
	});
};
const onApprovalMethod = () => {
	const list = checkBoxList.value;
	if (!list.length) return ElMessage.warning('请选择要发起审批的结算单');
	if (list.some((item) => item.status == '审核中' || item.status == '作废' || item.status == '审核通过')) return ElMessage.warning('勾选的结算单中存在审核中/作废/审核通过状态，不能发起审批');
	if (list.length === 1) {
		if (!list[0].id) return ElMessage.warning('单条结算单的 id 不能为空');
	} else {
		const supplier = list[0].supplierId;
		if (!supplier || !list.every((i) => i.supplierId === supplier)) {
			return ElMessage.warning('勾选的结算单 供应商名称 必须存在且一致');
		}
	}
	// 校验通过
	approvalVisible.value = true;
};

const handleSalesRatioChange = (e: any, type: string) => {
	const key = type === '销量比例' ? 'salesMark' : 'salesRatioMark';
	configurationInfo.value[key] = !e;
};

const handleThirtyDayNoSaleMarkChange = (e: any) => {
	configurationInfo.value.salesRatioMark = !!e;
	configurationInfo.value.salesMark = false;
	if (!e) {
		configurationInfo.value.salesRatioValue = undefined;
		configurationInfo.value.salesValue = undefined;
	}
};

const detailMethod = (row: any) => {
	if (!row.id) {
		ElMessage.warning('无结算单数据');
		return;
	}
	detailInfo.value.visible = true;
	detailInfo.value.data = row;
};

const onConfigurationMethod = async () => {
	const { data } = await GetCwDelayPaySupplierBalanceConfigAsync({});
	configurationInfo.value = Object.assign(configurationInfo.value, data || {});
	configurationVisible.value = true;
};

// 取消配置
const cancelConfiguration = () => {
	configurationVisible.value = false;
	// 重置配置信息
	configurationInfo.value = defaultCfg;
};

// 确定配置
const confirmConfiguration = () => {
	ElMessageBox.confirm(`是否确认配置?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const { success } = await AddOrUpdateCwDelayPaySupplierBalanceConfigAsync(configurationInfo.value);
			if (success) {
				ElMessage.success('配置成功');
				configurationVisible.value = false;
			}
		})
		.catch(() => {});
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'status', title: '状态', align: 'center' },
	{ sortable: true, field: 'source', title: '来源', align: 'center' },
	{ sortable: true, field: 'supplierName', title: '供应商名称', width: '250', align: 'center' },
	{ sortable: true, field: 'id', title: '结算单号', align: 'center' },
	{ sortable: true, field: 'goodsCode', title: '商品编码', align: 'center', width: '230' },
	{ sortable: true, field: 'createdUserName', title: '结算人', align: 'center' },
	{ sortable: true, field: 'balanceCount', title: '结算总数量', formatter: 'fmtAmt0', align: 'right' },
	{ sortable: true, field: 'balanceAmount', title: '结算总金额', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'createdTime', title: '结算时间', align: 'center', width: '135' },
	{
		title: '操作',
		align: 'center',
		width: '100',
		field: '202506251615',
		type: 'btnList',
		minWidth: '100',
		btnList: [{ title: '明细', handle: (row: any) => detailMethod(row) }],
		fixed: 'right',
	},
]);
</script>

<style scoped lang="scss">
.configuration-content {
	padding: 20px;

	.config-section {
		margin-bottom: 20px;

		.main-checkbox {
			font-size: 14px;
			font-weight: 500;
			color: #333;
		}
		.bottomMargin {
			margin-bottom: 15px;
		}

		.config-checkbox {
			font-size: 14px;
			color: #333;
			line-height: 1.5;
		}

		.sub-options {
			margin-left: 24px;
			padding: 15px;
			background-color: #f8f9fa;
			border-radius: 6px;
			border-left: 3px solid #64c5b1;
			display: flex;
			gap: 10px;

			.input-group {
				display: flex;
				align-items: center;
				// margin-bottom: 12px;

				&:last-child {
					margin-bottom: 0;
				}

				.input-label {
					font-size: 13px;
					color: #666;
					white-space: nowrap;
					margin-right: 8px;
				}
			}
		}
	}

	.dialog-footer {
		display: flex;
		justify-content: center;
		gap: 20px;
		margin-top: 30px;
		padding-top: 20px;
		border-top: 1px solid #ebeef5;

		.el-button {
			min-width: 80px;
			height: 36px;
			border-radius: 4px;
			font-weight: 500;

			&.cancel-btn {
				background-color: #f5f5f5;
				border-color: #d9d9d9;
				color: #666;

				&:hover {
					background-color: #e6e6e6;
					border-color: #b3b3b3;
				}
			}

			&.confirm-btn {
				background-color: #64c5b1;
				border-color: #64c5b1;

				&:hover {
					background-color: #7dd3c0;
					border-color: #7dd3c0;
				}
			}
		}
	}
}

// 复选框样式优化
:deep(.el-checkbox) {
	.el-checkbox__input {
		.el-checkbox__inner {
			width: 19px !important; // 默认14px + 5px = 19px
			height: 19px !important; // 默认14px + 5px = 19px

			&::after {
				width: 5px !important; // 增大√符号宽度
				height: 9px !important; // 增大√符号高度
				left: 6px !important; // 居中调整
				top: 2px !important; // 居中调整
			}
		}

		// 选中状态样式
		&.is-checked .el-checkbox__inner {
			background: #64c5b1 !important; // 使用新颜色
			border-color: #64c5b1 !important; // 使用新颜色
		}

		// 悬停状态样式
		&:hover .el-checkbox__inner {
			border-color: #64c5b1 !important; // 使用新颜色
		}
	}

	.el-checkbox__label {
		font-size: 14px;
		line-height: 1.5;
	}

	&.main-checkbox .el-checkbox__label {
		font-weight: 500;
		color: #333;
	}
}

// 输入框样式优化
:deep(.el-input) {
	.el-input__wrapper {
		border-radius: 4px;

		&:hover {
			border-color: #c0c4cc;
		}

		&.is-focus {
			border-color: #64c5b1;
		}
	}
}

// 圆形复选框样式
:deep(.round-checkbox) {
	.el-checkbox__input {
		.el-checkbox__inner {
			width: 19px !important; // 默认14px + 5px = 19px
			height: 19px !important; // 默认14px + 5px = 19px
			border-radius: 50% !important; // 设置为圆形

			&::after {
				width: 5px !important; // 增大√符号宽度
				height: 9px !important; // 增大√符号高度
				left: 6px !important; // 居中调整
				top: 2px !important; // 居中调整
			}
		}

		// 悬停状态样式
		&:hover .el-checkbox__inner {
			border-color: #64c5b1 !important; // 使用新颜色
		}

		// 选中状态的圆形样式
		&.is-checked .el-checkbox__inner {
			background: #64c5b1 !important; // 使用新颜色
			border: 1px solid #64c5b1 !important; // 使用新颜色
			border-radius: 50% !important;
			width: 19px !important;
			height: 19px !important;

			&::after {
				border-color: #ffffff !important; // 里面内容的颜色
				border-width: 2px !important; // 对里面的对号做一个加粗
				width: 5px !important; // 增大√符号宽度
				height: 9px !important; // 增大√符号高度
				left: 6px !important; // 居中调整
				top: 2px !important; // 居中调整
			}
		}

		// 禁用且选中状态的圆形样式
		&.is-disabled.is-checked .el-checkbox__inner {
			background: #dcdfe5 !important;
			border: 1px solid #dcdfe5 !important;
			border-radius: 50% !important; // 对边框进行调整
			width: 19px !important;
			height: 19px !important;

			&::after {
				border-color: #ffffff !important; // 里面内容的颜色
				border-width: 2px !important; // 对里面的对号做一个加粗
				width: 5px !important; // 增大√符号宽度
				height: 9px !important; // 增大√符号高度
				left: 6px !important; // 居中调整
				top: 2px !important; // 居中调整
			}
		}
	}
}
</style>
