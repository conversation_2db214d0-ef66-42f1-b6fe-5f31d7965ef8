<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange
					class="publicCss"
					v-model:startDate="query.productStartDate"
					v-model:endDate="query.productEndDate"
					style="width: 230px"
					start-placeholder="上新开始时间"
					end-placeholder="上新结束时间"
				/>
				<el-input v-model.trim="query.styleCode" class="publicCss" placeholder="款式编码" clearable maxlength="50" />
				<el-input v-model.trim="query.goodsCode" class="publicCss" placeholder="商品编码" clearable maxlength="50" />
				<el-input v-model.trim="query.goodsName" class="publicCss" placeholder="商品名称" clearable maxlength="50" />
				<el-select
					v-model="query.buyChoiceList"
					placeholder="采购"
					class="publicCss"
					style="width: 170px"
					:filter-method="(value: any) => buyChoiceMethod(value)"
					clearable
					filterable
					multiple
					collapse-tags
					collapse-tags-tooltip
					:reserve-keyword="false"
				>
					<el-option v-for="item in brandOption" :key="item.value" :label="item.label" :value="item.label"> </el-option>
				</el-select>
				<el-select v-model="query.isEnabled" placeholder="状态" class="publicCss" clearable filterable>
					<el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
				<div class="pb5">
					<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="202506281110" :keyField="'goodscode'" :is-asc="false" :tableCols="tableCols" showsummary isIndexFixed :query="query" :query-api="PageProductSellSituatioLitsAsync">
				<template #toolbar_buttons>
					<el-button @click="exportProps" type="primary" :disabled="isExport">导出</el-button>
				</template>
			</vxetable>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted } from 'vue';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
import { PageProductSellSituatioLitsAsync, ExportProductListAsync } from '/@/api/operatemanage/productSellSituation';
import { GetAllBianMaBrandAsync } from '/@/api/inventory/warehouse';
const brandList = ref<any[]>([]);
const brandOption = ref<any[]>([]);
const query = ref({
	productStartDate: dayjs().format('YYYY-MM-DD'), //上新开始时间
	productEndDate: dayjs().format('YYYY-MM-DD'), //上新结束时间
	styleCode: '', //款式编码
	goodsCode: '', //商品编码
	goodsName: '', //商品名称
	buyChoiceList: [], //采购
	isEnabled: '', //状态
});
const table = ref();
const isExport = ref(false);
const statusList = ref([
	{ label: '备用', value: 0 },
	{ label: '启用', value: 1 },
	{ label: '禁用', value: -1 },
]);
const buyChoiceMethod = (value: any) => {
	let a = brandList.value;
	if (value !== '') {
		brandOption.value = a.filter((item: any) => item.label.includes(value));
	} else {
		brandOption.value = a;
	}
};
const onSearch = () => {
	table.value.refreshTable(true);
};
const exportProps = () => {
	isExport.value = true;
	const params = { ...table.value.query, ...query.value };
	ExportProductListAsync(params)
		.then((data: any) => {
			if (data) {
				const aLink = document.createElement('a');
				let blob = new Blob([data], { type: 'application/vnd.ms-excel' });
				aLink.href = URL.createObjectURL(blob);
				aLink.setAttribute('download', '1688选品中心产品推新' + new Date().toLocaleString() + '.xlsx');
				aLink.click();
				isExport.value = false;
			}
		})
		.catch(() => {
			isExport.value = false;
		});
};
const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'productCreateDate', title: '上新日期', width: '100', formatter: 'formatDate' },
	{ sortable: true, field: 'productCreateDays', title: '上新天数', width: '100' },
	{ field: 'productImg', title: '图片', type: 'image', width: '70' },
	{ sortable: true, field: 'styleCode', title: '款式编码', width: '150' },
	{ sortable: true, field: 'goodsCode', title: '商品编码', width: '150' },
	{ sortable: true, field: 'goodsName', title: '商品名称', width: '150' },
	{ sortable: true, field: 'isEnabled', title: '状态', width: '100', formatter: (row: any) => statusList.value.find((item: any) => item.value === row.isEnabled)?.label },
	{
		sortable: true,
		field: 'providerLink',
		title: '供应链链接',
		width: '100',
		type: 'html',
		permissions: 'SupplyChainLinkPermission',
		formatter: (row: any) => (row.providerLink ? `<a href="${row.providerLink}" target="_blank">${row.providerLink}</a>` : ''),
	},
	{ sortable: true, field: 'costPrice', title: '成本价', width: '100', align: 'right' },
	{ sortable: true, field: 'remainNumber', title: '库存数量', width: '100', align: 'right' },
	{ sortable: true, field: 'brand', title: '采购', width: '100' },
	{ sortable: true, field: 'pinDuoDuo', title: '拼多多', width: '100', align: 'right' },
	{ sortable: true, field: 'douYin', title: '抖音', width: '100', align: 'right' },
	{ sortable: true, field: 'taoBao', title: '淘宝', width: '100', align: 'right' },
	{ sortable: true, field: 'kuaiShou', title: '快手', width: '100', align: 'right' },
	{ sortable: true, field: 'shiPinHao', title: '视频号', width: '100', align: 'right' },
	{ sortable: true, field: 'aLiBaBa', title: '阿里巴巴', width: '100', align: 'right' },
	{ sortable: true, field: 'jingDong', title: '京东', width: '100', align: 'right' },
	{ sortable: true, field: 'fengXiao', title: '分销', width: '100', align: 'right' },
]);
onMounted(async () => {
	const res = await GetAllBianMaBrandAsync({});
	if (!res?.success) return;
	brandList.value = res.data.map((item: any) => ({
		value: item.key,
		label: item.value,
	}));
});
</script>

<style scoped lang="scss"></style>
