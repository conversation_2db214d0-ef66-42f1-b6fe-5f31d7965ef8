<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange
					class="publicCss"
					v-model:startDate="query.productStartDate"
					v-model:endDate="query.productEndDate"
					style="width: 230px"
					start-placeholder="上新开始时间"
					end-placeholder="上新结束时间"
				/>
				<el-input v-model.trim="query.styleCode" class="publicCss" placeholder="款式编码" clearable maxlength="50" />
				<!-- <el-input v-model.trim="query.goodsCode" class="publicCss" placeholder="商品编码" clearable maxlength="50" />
				<el-input v-model.trim="query.goodsName" class="publicCss" placeholder="商品名称" clearable maxlength="50" />
				<el-select
					v-model="query.buyChoiceList"
					placeholder="采购"
					class="publicCss"
					style="width: 170px"
					:filter-method="(value: any) => buyChoiceMethod(value)"
					clearable
					filterable
					multiple
					collapse-tags
					collapse-tags-tooltip
					:reserve-keyword="false"
				>
					<el-option v-for="item in brandOption" :key="item.value" :label="item.label" :value="item.label"> </el-option>
				</el-select>
				<el-select v-model="query.isEnabled" placeholder="状态" class="publicCss" clearable filterable>
					<el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select> -->
				<div class="pb5">
					<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="details202508031031" :is-asc="false" :tableCols="tableCols" showsummary isIndexFixed :query="query" :query-api="PageProductSellSituatioLitsAsync"> </vxetable>
			<el-dialog v-model="codingInfo.visible" title="详情" width="70%" draggable overflow>
				<div>
					<dataRange
						class="publicCss"
						v-model:startDate="codingInfo.productStartDate"
						v-model:endDate="codingInfo.productEndDate"
						style="width: 230px"
						start-placeholder="上新开始时间"
						end-placeholder="上新结束时间"
					/>
				</div>
				<vxetable
					:height="'350'"
					v-if="codingInfo.visible"
					:isNeedQueryApi="false"
					:isNeedPager="false"
					:remote="false"
					:isNeedTools="false"
					ref="table1"
					id="processAutomationZtcIndex20250726163711"
					:tableCols="tableCols1"
					:data="codingInfo.marketCostInitiateDetailList"
					showsummary
				>
				</vxetable>
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted } from 'vue';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
import { PageProductSellSituatioLitsAsync, ExportProductListAsync } from '/@/api/operatemanage/productSellSituation';
import { GetAllBianMaBrandAsync } from '/@/api/inventory/warehouse';
const brandList = ref<any[]>([]);
const brandOption = ref<any[]>([]);
const query = ref({
	productStartDate: dayjs().format('YYYY-MM-DD'), //上新开始时间
	productEndDate: dayjs().format('YYYY-MM-DD'), //上新结束时间
	styleCode: '', //款式编码
	goodsCode: '', //商品编码
	goodsName: '', //商品名称
	buyChoiceList: [], //采购
	isEnabled: '', //状态
});
const table = ref();
const isExport = ref(false);
const statusList = ref([
	{ label: '备用', value: 0 },
	{ label: '启用', value: 1 },
	{ label: '禁用', value: -1 },
]);
const buyChoiceMethod = (value: any) => {
	let a = brandList.value;
	if (value !== '') {
		brandOption.value = a.filter((item: any) => item.label.includes(value));
	} else {
		brandOption.value = a;
	}
};
const onSearch = (teamData?: any, type?: string) => {
	if (teamData && type) {
		console.log('点击的团队数据:', teamData);
		console.log('入仓类型:', type);
		// 这里可以根据teamData和type设置查询条件
		// 例如：query.value.teamName = teamData.title;
	}
	// table.value.refreshTable(true);
};

// 暴露onSearch方法给父组件调用
defineExpose({
	onSearch,
});
const exportProps = () => {
	isExport.value = true;
	const params = { ...table.value.query, ...query.value };
	ExportProductListAsync(params)
		.then((data: any) => {
			if (data) {
				const aLink = document.createElement('a');
				let blob = new Blob([data], { type: 'application/vnd.ms-excel' });
				aLink.href = URL.createObjectURL(blob);
				aLink.setAttribute('download', '1688选品中心产品推新' + new Date().toLocaleString() + '.xlsx');
				aLink.click();
				isExport.value = false;
			}
		})
		.catch(() => {
			isExport.value = false;
		});
};

const detailedMethod = (row: any) => {
	console.log(row);
};
const tableCols1 = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'orderCount', title: '发货前退款单量', width: '100', align: 'right' },
	{ sortable: true, field: 'orderCount', title: '发货后退款单量', width: '100', align: 'right' },
	{ sortable: true, field: 'orderCount', title: '发货前退款率', width: '100', align: 'right' },
	{ sortable: true, field: 'orderCount', title: '发货后退款率', width: '100', align: 'right' },
	{ sortable: true, field: 'orderCount', title: '毛1', width: '100', align: 'right' },
	{ sortable: true, field: 'orderCount', title: '毛2', width: '100', align: 'right' },
	{ sortable: true, field: 'orderCount', title: '毛3', width: '100', align: 'right' },
	{ sortable: true, field: 'orderCount', title: '毛4', width: '100', align: 'right' },
	{ sortable: true, field: 'orderCount', title: '毛5', width: '100', align: 'right' },
	{ sortable: true, field: 'orderCount', title: '毛6', width: '100', align: 'right' },
]);
const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'saleAmont', title: '销售金额', width: 'auto', align: 'right', formatter: 'fmtAmt2' },
	{ sortable: true, field: 'orderCount', title: '订单量', width: 'auto', align: 'right' },
	{ sortable: true, field: 'profit6', title: '毛6', width: 'auto', align: 'right', type: 'click', handle: (row: any) => detailedMethod(row) },
	{ sortable: true, field: 'profit6Rate', title: '毛6率', width: 'auto', align: 'right', formatter: (row: any) => (row.profit6Rate ? (row.profit6Rate * 100).toFixed(2) + '%' : '0%') },
	{ sortable: true, field: 'platForm', title: '平台', width: 'auto', align: 'center', formatter: 'formatPlatform' },
]);
onMounted(async () => {
	const res = await GetAllBianMaBrandAsync({});
	if (!res?.success) return;
	brandList.value = res.data.map((item: any) => ({
		value: item.key,
		label: item.value,
	}));
});
</script>

<style scoped lang="scss"></style>
