<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="no-referrer" />
    <link rel="stylesheet" href="/approvalformCwgl/html/api/elment.css">
    <script src="/approvalformCwgl/html/api/vue.min.js"></script>
    <script src="/approvalformCwgl/html/api/elment.js"></script>
    <script src="/approvalformCwgl/html/api/jquery.min.js"></script>
    <script src="/approvalformCwgl/html/api/html2canvas.js"></script>
    <title>提现审批明细</title>
</head>

<body>
    <div id="app" style="margin:0 auto;overflow-y: hidden;">
        <el-container direction="vertical" style=" border: 1px #ccc solid;">
            <template>
                <el-table ref="tableBox" :data="data" align="center" style="width: 100%;" row-key="id" border
                    :max-height="tableHeight">
                    <el-table-column type="index" min-width="20" fixed></el-table-column>
                    <el-table-column prop="accountName" label="账户名" :show-overflow-tooltip="true"></el-table-column>
                    <el-table-column prop="account" label="账号" :show-overflow-tooltip="true"></el-table-column>
                    <el-table-column prop="operatorAmount" label="金额(元)"
                        :show-overflow-tooltip="true"></el-table-column>
                    <el-table-column prop="toAccountName" label="对方账户名" :show-overflow-tooltip="true"></el-table-column>
                </el-table>
            </template>
        </el-container>
    </div>
    <script>
        var vm = new Vue({
            el: '#app',
            data: function () {
                return {
                    thisLonding: true,
                    data: [],
                    tableHeight: null,
                    typeId: null,
                    currentPage: 1,
                    pageSize: 20,
                    total: 0,
                    title: ''
                }
            },
            created() {

            },
            async mounted() {
                this.getStyleSheetInfo();
                this.beginShowing()
            },
            methods: {
                beginShowing() {
                    this.$nextTick(function () {
                        // 文档显示区域的高度 -
                        if (this.$refs.tableBox) {
                            this.tableHeight = 835;
                            this.$refs.tableBox.doLayout()
                        }
                    })
                },
                async getStyleSheetInfo() {
                    var me = this;
                    let searchURL = window.location.search;
                    searchURL = searchURL.substring(1, searchURL.length);
                    let bulkId = searchURL.split("&")[0].split("=")[1];
                    me.thisLonding = true;
                    let parm = {};
                    $.ajax({
                        url: '/api/CwManage/WithDrawFlow/QueryWithDrawFlowByBulkId',
                        type: 'POST',
                        headers: {
                            'Content-Type': 'application/json;charset=UTF-8'
                        },
                        data: JSON.stringify({ bulkId: bulkId }),
                        success: function (response) {
                            me.data = response.data.list
                        },
                        error: function (xhr, textStatus, errorThrown) {
                        }
                    });
                },
            }
        });
    </script>
</body>

</html>