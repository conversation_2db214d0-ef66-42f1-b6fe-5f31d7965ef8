<template>
	<el-form ref="ruleFormRef" style="max-width: 600px" :model="ruleForm" status-icon :rules="rules" label-width="auto" class="demo-ruleForm">
		<el-form-item label="到访时间" prop="guestTime">
			<el-date-picker v-model="ruleForm.guestTime" class="w100" type="datetime" placeholder="到访时间" value-format="YYYY-MM-DD HH:mm:ss"  :clearable="false" />
		</el-form-item>
		<el-form-item label="来访意向" prop="guestIntent">
			<el-input type="textarea" row="3" maxlength="4000" v-model="ruleForm.guestIntent" autocomplete="off" resize="none"/>
		</el-form-item>
		<el-form-item label="接待人备注" prop="jdRemark">
			<el-input type="textarea" row="3" maxlength="4000" v-model.number="ruleForm.jdRemark" resize="none"/>
		</el-form-item>
		<el-form-item>
			<div class="mt10 w100" style="display: flex; justify-content: center">
				<el-button @click="emit('close')">取消</el-button>
				<el-button type="primary" @click="submitForm(ruleFormRef)" v-reclick="2000"> 提交 </el-button>
			</div>
		</el-form-item>
	</el-form>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { AddorEditCrmGuestRecord } from '/@/api/customerservice/customerGroup';
import dayjs from 'dayjs';
const emit = defineEmits(['close','getList']);
const ruleFormRef = ref<FormInstance>();
const props = defineProps({
	guestId: {
		type: String,
		default: '',
	},
});
const ruleForm = ref({
	guestTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
	guestIntent: '',
	jdRemark: '',
	guestId: props.guestId,
});

const rules = reactive<FormRules<typeof ruleForm>>({
	guestTime: [{ required: true, message: '请选择到访时间', trigger: 'change' }],
});

const submitForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	formEl.validate(async (valid) => {
		if (valid) {
			const { success } = await AddorEditCrmGuestRecord(ruleForm.value);
			if (success) {
				emit('close');
				emit('getList');
				window.$message({
					message: '新增成功',
					type: 'success',
				});
			} else {
				// Handle error
				console.error('Error submitting form:', success);
			}
			console.log('submit!');
		} else {
			console.log('error submit!');
		}
	});
};
</script>
