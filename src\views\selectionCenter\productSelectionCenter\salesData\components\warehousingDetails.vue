<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange
					startPlaceholder="日报开始日期"
					endPlaceholder="日报结束日期"
					class="publicCss"
					v-model:startDate="query.dayReportStartDate"
					v-model:endDate="query.dayReportEndDate"
					style="width: 230px"
				/>
				<el-input v-model.trim="query.styleCode" placeholder="款式编码" class="publicCss" clearable maxlength="50" />
				<el-input v-model.trim="query.goodsCode" placeholder="商品编码" class="publicCss" clearable maxlength="50" />
				<el-select v-model="query.platForm" placeholder="平台" class="publicCss" clearable filterable>
					<el-option v-for="item in platformlist" :key="item.value" :label="item.label" :value="item.value" />
				</el-select>
				<el-select filterable v-model="query.groupId" placeholder="运营组" class="publicCss" clearable>
					<el-option v-for="item in operationsTeamList" :key="item.key" :label="item.value" :value="item.key" />
				</el-select>
				<el-input v-model.trim="query.proCode" placeholder="ID" class="publicCss" clearable maxlength="50" />
				<div class="pb5">
					<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				height="450"
				id="202506091106"
				:tableCols="tableCols"
				showsummary
				isIndexFixed
				:query="query"
				:query-api="GetSelectionProductCenterSaleDataPlanADetail"
				:export-api="ExportSelectionProductCenterSaleDataPlanADetail"
				:asyncExport="{
					title: `明细 ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`,
					isAsync: false,
				}"
			></vxetable>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent, onMounted, PropType } from 'vue';
import { GetSelectionProductCenterSaleDataPlanADetail, ExportSelectionProductCenterSaleDataPlanADetail } from '/@/api/bookkeeper/selectionProductCenterSaleDataPlanA';
import dayjs from 'dayjs';
import { platformlist } from '/@/utils/tools';

interface OptionsProps {
	operationsTeamList: Array<{ key: string | number; value: string }>;
	rowData: Record<string, any>;
	parentData: Record<string, any>;
	fieldName: string;
	isSummary: boolean;
}

const props = defineProps({
	options: {
		type: Object as PropType<OptionsProps>,
		required: true,
	},
});

const { operationsTeamList, rowData, parentData, fieldName, isSummary } = props.options;

const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));

const query = ref({
	dayReportStartDate: '',
	dayReportEndDate: '',
	modeType: null,
	styleCode: null,
	goodsCode: null,
	proCode: null,
	groupId: null,
	platForm: null,
	dataType: parentData.dataType,
	isXuanPinXiaoShou: 2,
});

const table = ref();

const tableCols = ref<VxeTable.Columns[]>([
	// { sortable: true, field: 'yearMonthDayDate', title: '日期', width: '100', align: 'center', formatter: 'formatDate' },
	{ sortable: true, field: 'styleCode', title: '款式编码', width: 'auto', align: 'center' },
	{ sortable: true, field: 'goodsCode', title: '商品编码', width: 'auto', align: 'center' },
	{ sortable: true, field: 'saleAmont', title: '销售金额', width: 'auto', align: 'right', formatter: 'fmtAmt2' },
	{ sortable: true, field: 'orderCount', title: '订单量', width: 'auto', align: 'right' },
	{ sortable: true, field: 'profit6', title: '毛6', width: 'auto', align: 'right' },
	{ sortable: true, field: 'profit6Rate', title: '毛6率', width: 'auto', align: 'right', formatter: (row: any) => (row.profit6Rate ? (row.profit6Rate * 100).toFixed(2) + '%' : '0%') },
	{ sortable: true, field: 'platForm', title: '平台', width: '70', align: 'center', formatter: 'formatPlatform' },
	{ sortable: true, field: 'groupName', title: '运营组', width: 'auto', align: 'center' },
	{ sortable: true, field: 'proCode', title: 'ID', width: '145', align: 'center' },
	{ sortable: true, field: 'onTime', title: 'ID上架时间', width: 'auto', align: 'center' },
]);

onMounted(() => {
	// 设置查询条件
	query.value.modeType = rowData.modeType || null;

	// 设置日期
	const date = parentData.dayReportStartDate || dayjs().subtract(30, 'day').format('YYYY-MM-DD');
	query.value.dayReportStartDate = date;
	query.value.dayReportStartDate = date;
	query.value.dayReportEndDate = parentData.dayReportEndDate || dayjs().format('YYYY-MM-DD');
});
</script>

<style scoped lang="scss"></style>
