import request from '/@/utils/yhrequest';
import judgePort from '/@/utils/judgePort';
import type { AxiosRequestConfig, ResponseType } from 'axios';
const apiPrefix = `${judgePort() + import.meta.env.VITE_APP_BASE_API_CustomerService}/CustomerGroup/`;

//获取访问客户列表
export const GetCrmGuestList = (params: any) => {
	return request({
		url: apiPrefix + 'GetCrmGuestList',
		method: 'get',
		params,
	});
};

export const AddorEditCrmGuest = (params: any) => {
	return request({
		url: apiPrefix + 'AddorEditCrmGuest',
		method: 'post',
		data: params,
	});
};

export const GetCrmGuestRecordList = (params: any) => {
	return request({
		url: apiPrefix + 'GetCrmGuestRecordList',
		method: 'get',
		params,
	});
};

//新增访客记录 AddorEditCrmGuestRecord
export const AddorEditCrmGuestRecord = (params: any) => {
	return request({
		url: apiPrefix + 'AddorEditCrmGuestRecord',
		method: 'post',
		data: params,
	});
};

//查询一二级
export const GetCrmLeiMuList = (params: any) => {
	return request({
		url: apiPrefix + 'GetCrmLeiMuList',
		method: 'post',
		data: params,
	});
};

export const GetCrmGuestHfRecordList = (params: any) => {
	return request({
		url: apiPrefix + 'GetCrmGuestHfRecordList',
		method: 'get',
		params,
	});
};

//新增回访记录  AddorEditCrmGuestHfRecord
export const AddorEditCrmGuestHfRecord = (params: any) => {
	return request({
		url: apiPrefix + 'AddorEditCrmGuestHfRecord',
		method: 'post',
		data: params,
	});
};
//导出 ExportPremiumDeduction
export const ExportPremiumDeduction = (params: any, config: AxiosRequestConfig = { responseType: 'blob' as ResponseType }) => {
	return request.post(apiPrefix + 'ExportPremiumDeduction', params, config);
};

//产业带开发组效率统计查询
export const GetCrmEfficiencyStatisticsList = (params: any) => {
	return request.post(apiPrefix + 'GetCrmEfficiencyStatisticsList', params);
};

export const ExportCrmEfficiencyStatisticsList = (params: any, config: AxiosRequestConfig = { responseType: 'blob' as ResponseType }) => {
	return request.post(apiPrefix + 'ExportCrmEfficiencyStatisticsList', params, config);
};

//产特带开发组效率统计趋势图
export const QueryCrmEfficiencyStatisticsAnalysis = (params: any) => {
	return request.post(apiPrefix + 'QueryCrmEfficiencyStatisticsAnalysis', params);
};

//获取产业带开发二组组织架构
export const GetCrmEfficiencyDept = () => {
	return request.post(apiPrefix + 'GetCrmEfficiencyDept');
};
