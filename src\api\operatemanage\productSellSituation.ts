import request from '/@/utils/yhrequest';
import judgePort from '/@/utils/judgePort';
const apiPrefix = `${judgePort() + import.meta.env.VITE_APP_BASE_API_OperateManage}/ProductSellSituation/`;

// 1688选品中心产品推新-获取不同商品销量各平台情况
export const PageProductSellSituatioLitsAsync = (params: any) => request.post(apiPrefix + 'PageProductSellSituatioLitsAsync', params);

// 1688选品中心产品推新-导出不同商品销量各平台情况
export const ExportProductListAsync = (params: any, config: any = { responseType: 'blob' }) => request.post(apiPrefix + 'ExportProductListAsync', params, config);
