<template>
	<div class="initiate-approval-container">
		<!-- 可滚动区域 -->
		<div class="form-body">
			<el-form :model="ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="20">
					<!-- <el-col :span="24">
						<el-divider content-position="left">进货信息</el-divider>
					</el-col> -->
					<!-- 库存支持天数 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="库存支持天数" prop="inventorySupportDay">
							<el-input-number :controls="false" v-model="ruleForm.inventorySupportDay" :max="9999" :min="0" controls-position="right" :precision="0" placeholder="请输入库存支持天数" class="w100" />
						</el-form-item>
					</el-col>

					<!-- 费用分类 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="费用分类" prop="costClassificationList">
							<el-select v-model="ruleForm.costClassificationList" multiple collapse-tags collapse-tags-tooltip placeholder="请选择费用分类" clearable class="w100">
								<el-option v-for="item in expenseCategoryList" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>
					</el-col>

					<!-- 进货数量单位 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="是否是1688选品中心货物" prop="isOneSixEightEightCenterGood">
							<el-select v-model="ruleForm.isOneSixEightEightCenterGood" placeholder="请选择是否是1688选品中心货物" clearable class="w100">
								<el-option v-for="item in purchaseUnitList" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>
					</el-col>
					<!-- 进货数量 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="进货数量天数" prop="purchaseQuantityDays">
							<el-input-number :controls="false" v-model="ruleForm.purchaseQuantityDays" :max="9999" :min="0" controls-position="right" :precision="0" placeholder="请输入进货数量天数" class="w100" />
						</el-form-item>
					</el-col>

					<!-- 进货结算方式 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="货款结算方式" prop="paymentSettlementMethodList">
							<el-select v-model="ruleForm.paymentSettlementMethodList" multiple collapse-tags collapse-tags-tooltip placeholder="请选择货款结算方式" clearable class="w100">
								<el-option v-for="item in purchaseSettlementMethodList" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>
					</el-col>

					<!-- 外采预付款金额 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="外采预付审批编号" prop="externalProcurementPrepayApprovalNo">
							<el-input v-model.trim="ruleForm.externalProcurementPrepayApprovalNo" maxlength="100" clearable />
						</el-form-item>
					</el-col>

					<!-- 付款方式 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="付款方式" prop="payCategory">
							<el-select v-model="ruleForm.payCategory" placeholder="请选择付款方式" clearable class="w100">
								<el-option v-for="item in paymentMethodList" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>
					</el-col>

					<!-- 系统供应商名称/商品名称 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="系统供应商名称/商品名称" prop="systemApprovalName">
							<el-input v-model.trim="ruleForm.systemApprovalName" placeholder="请输入系统供应商名称/商品名称" maxlength="100" clearable />
						</el-form-item>
					</el-col>

					<!-- 采购单号 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="采购单号" prop="purchaseOrderNo">
							<el-input v-model.trim="ruleForm.purchaseOrderNo" placeholder="请输入采购单号" maxlength="100" clearable />
						</el-form-item>
					</el-col>

					<!-- 支付金额（元） -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="支付金额（元）" prop="payAmount">
							<el-input-number
								:controls="false"
								v-model="ruleForm.payAmount"
								:max="********"
								:min="0"
								:step="0.01"
								controls-position="right"
								:precision="4"
								placeholder="请输入支付金额"
								class="w100"
							/>
						</el-form-item>
					</el-col>
					<!-- 支付方式 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="支付方式" prop="payMethod">
							<el-select v-model="ruleForm.payMethod" placeholder="请选择支付方式" clearable class="w100">
								<el-option v-for="item in paymentTypeList" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>
					</el-col>

					<!-- 开户行 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="开户行" prop="openBank">
							<el-select v-model="ruleForm.openBank" placeholder="请选择开户行" clearable class="w100">
								<el-option v-for="item in bankNameList" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>
					</el-col>
					<!-- 账户名 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="账户名" prop="accountName">
							<el-input v-model.trim="ruleForm.accountName" placeholder="请输入账户名" type="textarea" resize="none" :rows="3" maxlength="8000" show-word-limit clearable />
						</el-form-item>
					</el-col>

					<!-- 账号 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="账号" prop="accountNo">
							<el-input v-model.trim="ruleForm.accountNo" placeholder="请输入账号" type="textarea" resize="none" :rows="3" maxlength="8000" show-word-limit clearable />
						</el-form-item>
					</el-col>

					<!-- 税费付款方式 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="税费付款方式" prop="taxPayCategory">
							<el-select v-model="ruleForm.taxPayCategory" placeholder="请选择税费付款方式" clearable class="w100">
								<el-option v-for="item in taxPaymentMethodList" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>
					</el-col>

					<!-- 税费金额 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="税费金额" prop="taxAmount">
							<el-input-number
								:controls="false"
								v-model="ruleForm.taxAmount"
								:max="********"
								:min="0"
								:step="0.01"
								controls-position="right"
								:precision="4"
								placeholder="请输入税费金额"
								class="w100"
							/>
						</el-form-item>
					</el-col>

					<!-- 个人账户名 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="个人账户名" prop="personalAccountName">
							<el-input v-model.trim="ruleForm.personalAccountName" placeholder="请输入个人账户名" type="textarea" resize="none" :rows="3" maxlength="8000" show-word-limit clearable />
						</el-form-item>
					</el-col>

					<!-- 个人账号 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="个人账号" prop="personalAccountNo">
							<el-input v-model.trim="ruleForm.personalAccountNo" placeholder="请输入个人账号" type="textarea" resize="none" :rows="3" maxlength="8000" show-word-limit clearable />
						</el-form-item>
					</el-col>

					<!-- 个人开户行 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="个人开户行" prop="personalOpenBank">
							<el-select v-model="ruleForm.personalOpenBank" placeholder="请选择个人开户行" clearable class="w100">
								<el-option v-for="item in personalBankNameList" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>
					</el-col>

					<!-- 备注 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="备注" prop="remark">
							<el-input v-model.trim="ruleForm.remark" type="textarea" resize="none" :rows="3" :maxlength="8000" show-word-limit placeholder="请输入备注" />
						</el-form-item>
					</el-col>

					<!-- 图片上传区域 -->
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="图片" prop="picturesList">
							<!-- 这里预留图片上传组件位置 -->
							<!-- <div class="upload-placeholder">
								<el-text type="info">图片上传组件（待实现）</el-text>
							</div> -->
							<uploadMf v-if="editPriceVisible" v-model:imagesStr="ruleForm.picturesList" uploadName="上传图片" :upstyle="{ height: 40, width: 40 }" :limit="9"></uploadMf>
						</el-form-item>
					</el-col>

					<!-- 附件上传区域 -->
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="附件" prop="attachmentsList">
							<!-- 这里预留附件上传组件位置 -->
							<!-- <div class="upload-placeholder">
								<el-text type="info">附件上传组件（待实现）</el-text>
							</div> -->
							<uploadMf
								ref="uploadMfRef"
								:isImage="false"
								v-if="editPriceVisible"
								v-model:imagesStr="ruleForm.attachmentsList"
								uploadName="上传文件"
								uploadFormat=".pdf,.doc,.docx,.xls,.xlsx"
								:upstyle="{ height: 40, width: 40 }"
								:limit="9"
							></uploadMf>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</div>
		<!-- 固定在底部，不随滚动条滚动 -->
		<div class="form-footer">
			<el-button @click="cancel">取消</el-button>
			<el-button type="primary" @click="submit">提交</el-button>
		</div>
	</div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, defineAsyncComponent, withDefaults, defineProps } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormRules, FormInstance } from 'element-plus';
const uploadMf = defineAsyncComponent(() => import('/@/components/yhCom/uploadMf.vue'));
import { GetCwDelayPaySupplierBalanceRecordApplyData, SendCwDelayPaySupplierBalanceRecordApply } from '/@/api/cwManager/cwDelayPay';

// 父级传递来的函数，用于回调
const emit = defineEmits(['reloadTable', 'closeApproval']);

interface BalanceRow {
	instanceId?: string | null;
	[key: string]: any;
}

const props = withDefaults(defineProps<{ checkBoxList: BalanceRow[] }>(), {
	checkBoxList: () => [],
});

// 定义变量内容
const ruleFormRef = ref<FormInstance>();
const loading = ref(false);
const editPriceVisible = ref(false);
const uploadMfRef = ref<any>();

// 表单数据
const ruleForm = reactive({
	inventorySupportDay: undefined, // 库存支持天数
	costClassificationList: [], // 费用分类
	isOneSixEightEightCenterGood: '', // 是否是1688选品中心货物
	purchaseQuantityDays: undefined, // 进货数量天数
	paymentSettlementMethodList: [], // 进货结算方式
	externalProcurementPrepayApprovalNo: '', // 外采预付款金额
	payCategory: '', // 付款方式
	systemApprovalName: '', // 系统供应商名称/商品名称
	purchaseOrderNo: '', // 采购单号
	payAmount: undefined, // 支付金额（元）
	payMethod: '', // 支付方式
	accountName: '', // 账户名
	accountNo: '', // 账号
	openBank: '', // 开户行
	taxPayCategory: '', // 税费付款方式
	taxAmount: undefined, // 税费金额
	personalAccountName: '', // 个人账户名
	personalAccountNo: '', // 个人账号
	personalOpenBank: '', // 个人开户行
	remark: '', // 备注
	picturesList: [], // 图片
	attachmentsList: [], // 附件
} as any);

// 下拉选项列表
const expenseCategoryList = ref([
	{ label: '成品货款', value: '成品货款' },
	{ label: '加工费', value: '加工费' },
	{ label: '辅料', value: '辅料' },
	{ label: '包装厂采购', value: '包装厂采购' },
	{ label: '加工厂采购', value: '加工厂采购' },
	{ label: '定制包装押金', value: '定制包装押金' },
	{ label: '供销货款', value: '供销货款' },
	{ label: '1688货款', value: '1688货款' },
]);

const purchaseUnitList = ref([
	{ label: '是', value: '是' },
	{ label: '否', value: '否' },
]);

const purchaseSettlementMethodList = ref([
	{ label: '月结', value: '月结' },
	{ label: '货到付款', value: '货到付款' },
	{ label: '预付款', value: '预付款' },
	{ label: '尾款', value: '尾款' },
	{ label: '外采预付款', value: '外采预付款' },
]);

const paymentMethodList = ref([
	{ label: '对公付款', value: '对公付款' },
	{ label: '对私付款', value: '对私付款' },
	{ label: '抵扣（有预付款，申请抵扣）', value: '抵扣（有预付款，申请抵扣）' },
]);

const paymentTypeList = ref([
	{ label: '阿里巴巴', value: '阿里巴巴' },
	{ label: '支付宝', value: '支付宝' },
	{ label: '微信', value: '微信' },
	{ label: '银行卡', value: '银行卡' },
	{ label: '现金', value: '现金' },
]);

const bankNameList = ref([
	{ label: '招商银行', value: '招商银行' },
	{ label: '中国工商银行', value: '中国工商银行' },
	{ label: '农商银行', value: '农商银行' },
	{ label: '中信银行', value: '中信银行' },
	{ label: '中国农业银行', value: '中国农业银行' },
	{ label: '中国建设银行', value: '中国建设银行' },
	{ label: '交通银行', value: '交通银行' },
	{ label: '浙商银行', value: '浙商银行' },
	{ label: '其他（备注）', value: '其他（备注）' },
]);

const taxPaymentMethodList = ref([
	{ label: '税费公转', value: '税费公转' },
	{ label: '税费私转', value: '税费私转' },
	{ label: '货款含税', value: '货款含税' },
]);

const personalBankNameList = ref([
	{ label: '招商银行', value: '招商银行' },
	{ label: '中国工商银行', value: '中国工商银行' },
	{ label: '农商银行', value: '农商银行' },
	{ label: '中信银行', value: '中信银行' },
	{ label: '中国农业银行', value: '中国农业银行' },
	{ label: '中国建设银行', value: '中国建设银行' },
	{ label: '交通银行', value: '交通银行' },
	{ label: '浙商银行', value: '浙商银行' },
	{ label: '其他（备注）', value: '其他（备注）' },
]);

// 表单验证规则
const rules = ref<FormRules>({
	inventorySupportDay: [{ required: true, message: '请输入库存支持天数', trigger: 'blur' }],
	costClassificationList: [{ required: true, message: '请选择费用分类', trigger: 'change' }],
	purchaseQuantityDays: [{ required: true, message: '请输入进货数量', trigger: 'blur' }],
	isOneSixEightEightCenterGood: [{ required: true, message: '请选择是否是1688货物', trigger: 'change' }],
	paymentSettlementMethodList: [{ required: true, message: '请选择结算方式', trigger: 'change' }],
	payCategory: [{ required: true, message: '请选择付款方式', trigger: 'change' }],
	systemApprovalName: [{ required: true, message: '请输入系统供应商名称/商品名称', trigger: 'blur' }],
	payAmount: [{ required: true, message: '请输入支付金额', trigger: 'blur' }],
	payMethod: [{ required: true, message: '请选择支付方式', trigger: 'change' }],
	accountName: [{ required: true, message: '请输入账户名', trigger: 'blur' }],
	accountNo: [{ required: true, message: '请输入账号', trigger: 'blur' }],
	openBank: [{ required: true, message: '请选择开户行', trigger: 'change' }],
	purchaseOrderNo: [{ required: true, message: '请输入采购单号', trigger: 'blur' }],
	externalProcurementPrepayApprovalNo: [{ required: true, message: '请输入外部采购预付款审批单号', trigger: 'blur' }],
});

// 页面加载时
onMounted(async () => {
	// 初始化操作
	initForm();
	editPriceVisible.value = true;
});

// 初始化表单
const initForm = () => {
	// 清除表单验证状态
	if (ruleFormRef.value) {
		ruleFormRef.value?.resetFields();
		ruleFormRef.value?.clearValidate();
	}

	// 重置表单数据
	Object.assign(ruleForm, {
		inventorySupportDay: undefined,
		costClassificationList: [],
		isOneSixEightEightCenterGood: '',
		purchaseQuantityDays: undefined,
		paymentSettlementMethodList: [],
		externalProcurementPrepayApprovalNo: '',
		payCategory: '',
		systemApprovalName: '',
		purchaseOrderNo: '',
		payAmount: undefined,
		payMethod: '',
		accountName: '',
		accountNo: '',
		openBank: '',
		taxPayCategory: '',
		taxAmount: undefined,
		personalAccountName: '',
		personalAccountNo: '',
		personalOpenBank: '',
		remark: '',
		picturesList: [],
		attachmentsList: [],
	});
};

// 取消
const cancel = () => {
	emit('closeApproval');
};

// 提交
const submit = async () => {
	let attachmentFile = uploadMfRef.value.fileList && uploadMfRef.value.fileList?.length > 0 ? JSON.stringify(uploadMfRef.value.fileList) : '';
	console.log(attachmentFile, 'attachmentFile');
	if (!ruleFormRef.value) return;

	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			try {
				loading.value = true;
				const { success } = await SendCwDelayPaySupplierBalanceRecordApply({ ...ruleForm, checkBalanceIds: props.checkBoxList.map((item) => item.id) });
				if (success) {
					ElMessage.success('提交成功');
					emit('reloadTable');
					emit('closeApproval');
				}
			} catch (error) {
				console.error('提交失败:', error);
				ElMessage.error('提交失败，请重试');
			} finally {
				loading.value = false;
			}
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields || {}).length}处验证失败，请修改后再提交`,
				type: 'error',
			});
		}
	});
};

// 将属性或者函数暴露给父组件
defineExpose({ initForm });
</script>

<style scoped lang="scss">
.initiate-approval-container {
	display: flex;
	flex-direction: column;

	:deep(.el-select),
	:deep(.el-input-number) {
		width: 100%;
	}

	.upload-placeholder {
		display: flex;
		align-items: center;
		justify-content: center;
		min-height: 80px;
		border: 2px dashed #dcdfe6;
		border-radius: 6px;
		background-color: #fafafa;
		color: #909399;
		font-size: 14px;

		&:hover {
			border-color: #64c5b1;
			color: #64c5b1;
		}
	}

	.form-body {
		flex: 1 1 auto;
		max-height: 700px;
		overflow-y: auto;
		padding: 20px;
	}

	.form-footer {
		flex: 0 0 auto;
		display: flex;
		justify-content: flex-end;
		gap: 12px;
		margin-top: 20px;
	}

	.mb20 {
		margin-bottom: 20px;
	}

	.w100 {
		width: 100%;
	}
}
</style>
