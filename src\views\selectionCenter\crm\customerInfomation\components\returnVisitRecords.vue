<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange class="publicCss" v-model:startDate="query.startHfTime" v-model:endDate="query.endHfTime"
					style="width: 230px" start-placeholder="回访时间" end-placeholder="回访时间" />
				<dataRange class="publicCss" v-model:startDate="query.startAddTime" v-model:endDate="query.endAddTime"
					style="width: 230px" start-placeholder="登记开始时间" end-placeholder="登记结束时间" />
				<div class="pb5">
					<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" orderBy="addTime" :is-asc="false" id="20250510133901" :tableCols="tableCols"
				showsummary isIndexFixed :query="query" :query-api="GetCrmGuestHfRecordList"> </vxetable>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent } from 'vue';
import dayjs from 'dayjs';
import { GetCrmGuestHfRecordList } from '/@/api/customerservice/customerGroup';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
import { platformlist } from '/@/utils/tools';
const props = defineProps({
	hfId: {
		type: String,
		default: '',
	},
});
const query = ref({
	hfId: props.hfId,
	startHfTime: '',
	endHfTime: '',
	startAddTime: '',
	endAddTime: '',
});
const table = ref();
const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'hfTime', title: '回访时间', formatter: 'formatDate' },
	{ sortable: true, field: 'hfIntent', title: '回访意向' },
	{ sortable: true, field: 'hfRemark', title: '回访人备注' },
	{ sortable: true, field: 'hfStatus', title: '回访状态' },
	{ sortable: true, field: 'hfUserName', title: '回访人姓名' },
	{ sortable: true, field: 'addTime', title: '登记时间', formatter: 'formatDate' },
]);
</script>

<style scoped lang="scss"></style>
