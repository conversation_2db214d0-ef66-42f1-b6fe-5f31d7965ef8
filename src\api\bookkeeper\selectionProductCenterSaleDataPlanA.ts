import request from '/@/utils/yhrequest';
import judgePort from '/@/utils/judgePort';
import type { AxiosRequestConfig, ResponseType } from 'axios';
const apiPrefix = `${judgePort() + import.meta.env.VITE_APP_BASE_API_BookKeeper}/SelectionProductCenterSaleDataPlanA/`;

// 查询选品中心销售数据信息
export const GetSelectionProductCenterSaleDataPlanA = (params: any) => {
	return request({ url: apiPrefix + 'GetSelectionProductCenterSaleDataPlanA', method: 'post', data: params });
};

// 查询选品中心销售数据信息明细
export const GetSelectionProductCenterSaleDataPlanADetail = (params: any) => {
	return request({ url: apiPrefix + 'GetSelectionProductCenterSaleDataPlanADetail', method: 'post', data: params });
};

// 获取选品中心销售数据趋势图
export const QuerySelectionProductCenterSaleDataPlanAAnalysis = (params: any) => {
	return request({ url: apiPrefix + 'QuerySelectionProductCenterSaleDataPlanAAnalysis', method: 'post', data: params });
};

// 导出选品中心销售数据信息明细
export const ExportSelectionProductCenterSaleDataPlanADetail = (params: any, config: AxiosRequestConfig = { responseType: 'blob' as ResponseType }) => {
	return request.post(apiPrefix + 'ExportSelectionProductCenterSaleDataPlanADetail', params, config);
};

// 导出选品中心销售数据信息
export const ExportSelectionProductCenterSaleDataPlanA = (params: any, config: AxiosRequestConfig = { responseType: 'blob' as ResponseType }) => {
	return request.post(apiPrefix + 'ExportSelectionProductCenterSaleDataPlanA', params, config);
};
