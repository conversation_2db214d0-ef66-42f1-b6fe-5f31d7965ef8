<template>
	<el-form ref="ruleFormRef" style="max-width: 600px" :model="ruleForm" status-icon :rules="rules" label-width="auto"
		class="demo-ruleForm">
		<el-form-item label="回访时间" prop="hfTime">
			<el-date-picker v-model="ruleForm.hfTime" class="w100" type="datetime" placeholder="回访时间"
				value-format="YYYY-MM-DD HH:mm:ss" :clearable="false" />
		</el-form-item>
		<el-form-item label="回访状态">
			<el-select v-model="ruleForm.hfStatus" placeholder="回访状态" class="publicCss" @change="changeStatus">
				<el-option label="跟进" value="跟进" />
				<el-option label="丢失" value="丢失" />
				<el-option label="转化" value="转化" />
			</el-select>
		</el-form-item>
		<el-form-item label="丢失原因" v-if="ruleForm.hfStatus == '丢失'" prop="lostReason">
			<el-input type="textarea" row="3" maxlength="200" placeholder="丢失原因" v-model="ruleForm.lostReason" resize="none"/>
		</el-form-item>
		<el-form-item label="回访意向">
			<el-input type="textarea" row="3" maxlength="4000" placeholder="回访意向" v-model="ruleForm.hfIntent"resize="none"
				autocomplete="off" />
		</el-form-item>
		<el-form-item label="回访人备注">
			<el-input type="textarea" row="3" maxlength="4000" placeholder="回访人备注" v-model="ruleForm.hfRemark" resize="none"/>
		</el-form-item>
		<el-form-item>
			<div class="mt10 w100" style="display: flex; justify-content: center">
				<el-button @click="emit('close')">取消</el-button>
				<el-button type="primary" @click="submitForm(ruleFormRef)" v-reclick="2000"> 提交 </el-button>
			</div>
		</el-form-item>
	</el-form>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { AddorEditCrmGuestHfRecord } from '/@/api/customerservice/customerGroup';
import dayjs from 'dayjs';
const emit = defineEmits(['close', 'getList']);
const props = defineProps({
	guestId: {
		type: String,
		default: '',
	},
});
const ruleFormRef = ref<FormInstance>();
const ruleForm = ref({
	hfTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
	hfIntent: '',
	hfRemark: '',
	guestId: props.guestId,
	hfStatus: '',
	lostReason: '',
});
const rules = reactive<FormRules<typeof ruleForm>>({
	hfTime: [{ required: true, message: '请选择回访时间', trigger: 'change' }],
	lostReason: [{ required: true, message: '请输入丢失原因', trigger: 'blur' }],
});

const changeStatus = (val: string) => {
	if (val != '丢失') {
		ruleForm.value.lostReason = '';
	}
};

const submitForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	formEl.validate(async (valid) => {
		if (valid) {
			const { success } = await AddorEditCrmGuestHfRecord(ruleForm.value);
			if (success) {
				emit('close');
				emit('getList');
				window.$message({
					message: '新增回访记录成功',
					type: 'success',
				});
			}
		} else {
			console.log('error submit!');
		}
	});
};
</script>
