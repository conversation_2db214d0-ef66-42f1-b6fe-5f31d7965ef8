<template>
	<div class="w100 h100">
		<Container>
			<template #header>
				<div class="topCss">
					<el-input v-model="query.keyWords" placeholder="关键字查询,厂家名称、厂家姓名等" class="publicCss"
						style="width: 230px" maxlength="200" clearable />
					<dataRange class="publicCss" v-model:startDate="query.startLastHfTime"
						v-model:endDate="query.endLastHfTime" style="width: 230px" start-placeholder="拜访时间"
						end-placeholder="拜访时间" />
					<el-select v-model="query.status" placeholder="状态" class="publicCss" clearable>
						<el-option v-for="item in statusList" :key="item.value" :label="item.label"
							:value="item.value" />
					</el-select>
					<div class="pb5">
						<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
					</div>
				</div>
			</template>
			<template #content>
				<vxetable ref="table" id="20250611153946" :tableCols="tableCols" showsummary :query="query"
					:query-api="GetCrmGuestList" :export-api="ExportPremiumDeduction" :asyncExport="{
						title: '业务外出',
						isAsync: false,
					}">
				</vxetable>
			</template>
		</Container>

		<el-dialog draggable overflow v-model="recordsVisible" title="跟进记录" width="60%" :close-on-click-modal="false"
			:before-close="close">
			<returnRecords v-if="recordsVisible" style="height: 700px" :hfId="hfId" @close="close" />
		</el-dialog>

		<el-dialog draggable overflow v-model="addVisitorVisible" :title="isAdd ? '新增访客' : '编辑访客'" width="70%"
			:close-on-click-modal="false">
			<addVisitor v-if="addVisitorVisible" :info="fkForm" @close="addVisitorVisible = false"
				@getList="table.refreshTable(true)" />
		</el-dialog>

		<el-drawer v-model="drawer" title="预览附件" direction="btt" size="80%">
			<VueOfficeExcel :src="excelUrl" style="height: 100%" />
		</el-drawer>
	</div>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent } from 'vue';
import dayjs from 'dayjs';
import { downLoadFile } from '/@/utils/tools';
import { GetCrmGuestList, ExportPremiumDeduction } from '/@/api/customerservice/customerGroup';
import VueOfficeExcel from '@vue-office/excel/lib/v3/vue-office-excel.mjs';
import '@vue-office/excel/lib/v3/index.css';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable.vue'));
const numRange = defineAsyncComponent(() => import('/@/components/yhCom/numRange.vue'));
const addVisitor = defineAsyncComponent(() => import('./components/addVisitor.vue'));
const returnRecords = defineAsyncComponent(() => import('./components/returnRecords.vue'));
const recordsVisible = ref(false);
const returnVisible = ref(false);
const addRecordsVisible = ref(false);
const addReturnVisible = ref(false);
const addVisitorVisible = ref(false);
const query = ref({
	keyWords: '',
	startFirstGuestTime: '',
	endFirstGuestTime: '',
	startLastGuestTime: '',
	endLastGuestTime: '',
	startFirstAddTime: '',
	endFirstAddTime: '',
	startAddTime: '',
	endAddTime: '',
	startFirstHfTime: '',
	endFirstHfTime: '',
	startLastHfTime: '',
	endLastHfTime: '',
	sex: '',
	isConvert: '',
	guestType: '',
	status: '',
	customerSource: '业务外出',
});
const statusList = ref([
	{ label: '跟进', value: '跟进' },
	{ label: '丢失', value: '丢失' },
	{ label: '已拉群', value: '已拉群' },
	{ label: '已入仓', value: '已入仓' },
]);

const fkForm = ref({});
const fkId = ref('');
const guestId = ref('');
const hfId = ref('');
const table = ref();
const isAdd = ref(false);
const excelUrl = ref('');
const drawer = ref(false);

const visitorLog = (row: any) => {
	hfId.value = row.id;
	recordsVisible.value = true;
};

const visitorRegistration = (type: boolean, row: any) => {
	isAdd.value = type;
	fkForm.value = row;
	addVisitorVisible.value = true;
};

const previewFile = (attachmentFile: string) => {
	const res = JSON.parse(attachmentFile);
	drawer.value = true;
	excelUrl.value = res[0].url;
};

const close = () => {
	recordsVisible.value = false;
	returnVisible.value = false;
	table.value.refreshTable(true);
};

const downLoad = (attachmentFile: string) => {
	const res = JSON.parse(attachmentFile);
	downLoadFile(res[0].url, res[0].name);
};
const tableCols = ref<VxeTable.Columns[]>([
	{ width: 130, sortable: true, field: 'cjName', title: '厂家名称' },
	{ width: 80, align: 'center', sortable: true, field: 'realName', title: '厂家姓名' },
	{ width: 70, field: 'picture', title: '定位图片', type: 'image' },
	{ width: 120, field: 'positioning', title: '定位' },
	{ width: 120, sortable: true, field: 'lastHfTime', title: '拜访时间', formatter: 'formatDate', align: 'center' },
	{ width: 80, visible: false, sortable: true, field: 'mainBusinessCategories', title: '类目描述' },
	{ width: 120, sortable: false, field: 'businessCategory', title: '主经营类目' },
	{ width: 120, sortable: false, field: 'categoryName1', title: '主一级类目' },
	{ width: 120, sortable: false, field: 'categoryName2', title: '主二级类目' },
	{ width: 120, visible: false, sortable: false, field: 'fuMainBusinessCategories', title: '辅经营类目' },
	{ width: 120, visible: false, sortable: false, field: 'fuCategoryName1', title: '辅一级类目' },
	{ width: 120, visible: false, sortable: false, field: 'fuCategoryName2', title: '辅二级类目' },
	{
		width: 70,
		align: 'center',
		sortable: true,
		field: 'status',
		title: '状态',
		formatter: (row: any) => (row.status !== null ? statusList.value.find((item: any) => item.value == row.status)?.label : row.status),
	},
	{ width: 90, sortable: true, field: 'warehousingNo', title: '入库单号' },
	{ width: 90, sortable: true, field: 'lostReason', title: '丢失原因' },
	{ width: 200, sortable: true, field: 'yuChangJiaGouTongNeiRong', title: '与厂家沟通内容' },
	{ width: 200, sortable: true, field: 'intent', title: '厂家描述' },
	{ width: 120, sortable: true, field: 'jiHuaHfTime', title: '计划回访时间', formatter: 'formatDate', align: 'center' },
	{ width: 200, sortable: true, field: 'jiHuaHuiFangGouTongNeiRong', title: '计划回访沟通内容' },
	{ width: 90, sortable: true, field: 'contactPhone', title: '联系电话' },
	{ width: 90, sortable: true, field: 'cjAddr', title: '厂家地址' },
	{ width: 105, sortable: true, field: 'addUserName', title: '业务员' },
	{
		width: 150,
		field: '20250611153953',
		title: '操作',
		type: 'btnList',
		align: 'center',
		fixed: 'right',
		btnList: [
			{ title: '编辑', handle: (row: any) => visitorRegistration(false, row), isDisabled: (row: any) => row.isEdit != 1 },
			{ title: '跟进记录', handle: (row: any) => visitorLog(row), formatter: (row: any) => `跟进记录${row.hfTimes ?? 0}条`, isDisabled: (row: any) => row.isEdit != 1 },
		],
	},
]);
</script>

<style scoped lang="scss"></style>
