import request from '/@/utils/yhrequest';
import judgePort from '/@/utils/judgePort';
const apiPrefix = `${judgePort() + import.meta.env.VITE_APP_BASE_API}/loginlog/`;

// 访问日志
export const PageReqLog = (module: string, page: string, tab: string, action: string, config = {}) => {
	function removeSpaces(str: string) {
		return str.replace(/\s+/g, '');
	}
	let p2 = { ModuleName: removeSpaces(module), PageName: removeSpaces(page), PageTab: removeSpaces(tab), ActionName: removeSpaces(action), AppType: '选品中心ERP', ReqPath: window.location.pathname };
	return request.post(apiPrefix + 'PageReqLog', p2, config);
};
