<template>
	<div class="storage_Css">
		<div class="left_Css">
			<!-- Left Section: Free Warehousing -->
			<div class="section_container">
				<span class="section_title">免费入仓</span>
				<div class="data_container">
					<div v-for="(team, index) in freeWarehousingList" :key="index" class="team_card">
						<div class="team_title">{{ team.title }}</div>
						<div class="team_metrics">
							<div class="metric_item">
								<span class="metric_label">在线ID数量:</span>
								<span class="metric_value">{{ team.onLineIdCount }}</span>
							</div>
							<div class="metric_item">
								<span class="metric_label">商品编码数量:</span>
								<span class="metric_value">{{ team.goodsCodeCount }}</span>
							</div>
							<div class="metric_item">
								<span class="metric_label">销售金额:</span>
								<span class="metric_value">{{ team.saleAmount }}</span>
							</div>
							<div class="metric_item">
								<span class="metric_label">毛6:</span>
								<span class="metric_value">{{ team.profit6 }}</span>
							</div>
							<div class="metric_item">
								<span class="metric_label">订单数量:</span>
								<span class="metric_value">{{ team.orderCount }}</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Right Section: Procurement Warehousing -->
			<div class="section_container">
				<span class="section_title">采购入仓</span>
				<div class="data_container">
					<div v-for="(team, index) in procurementWarehousingList" :key="index" class="team_card">
						<div class="team_title">{{ team.title }}</div>
						<div class="team_metrics">
							<div class="metric_item">
								<span class="metric_label">采购数量:</span>
								<span class="metric_value">{{ team.purchaseCount }}</span>
							</div>
							<div class="metric_item">
								<span class="metric_label">入仓金额:</span>
								<span class="metric_value">{{ team.warehousingAmount }}</span>
							</div>
							<div class="metric_item">
								<span class="metric_label">供应商数量:</span>
								<span class="metric_value">{{ team.supplierCount }}</span>
							</div>
							<div class="metric_item">
								<span class="metric_label">商品种类:</span>
								<span class="metric_value">{{ team.productTypes }}</span>
							</div>
							<div class="metric_item">
								<span class="metric_label">完成率:</span>
								<span class="metric_value">{{ team.completionRate }}%</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts" name="WarehousingStorage">
import { ref } from 'vue';

// Free Warehousing data
const freeWarehousingList = ref([
	{
		title: '张三团队',
		onLineIdCount: '123',
		goodsCodeCount: '12',
		saleAmount: '345',
		profit6: '234',
		orderCount: '124',
	},
	{
		title: '李四团队',
		onLineIdCount: '156',
		goodsCodeCount: '18',
		saleAmount: '428',
		profit6: '298',
		orderCount: '167',
	},
	{
		title: '王五团队',
		onLineIdCount: '89',
		goodsCodeCount: '9',
		saleAmount: '267',
		profit6: '178',
		orderCount: '95',
	},
	{
		title: '赵六团队',
		onLineIdCount: '201',
		goodsCodeCount: '25',
		saleAmount: '512',
		profit6: '356',
		orderCount: '189',
	},
	{
		title: '孙七团队',
		onLineIdCount: '134',
		goodsCodeCount: '15',
		saleAmount: '389',
		profit6: '267',
		orderCount: '142',
	},
]);

// Procurement Warehousing data (placeholder structure)
const procurementWarehousingList = ref([
	{
		title: '采购团队A',
		purchaseCount: '89',
		warehousingAmount: '156,789',
		supplierCount: '23',
		productTypes: '45',
		completionRate: '87',
	},
	{
		title: '采购团队B',
		purchaseCount: '67',
		warehousingAmount: '134,567',
		supplierCount: '18',
		productTypes: '38',
		completionRate: '92',
	},
	{
		title: '采购团队C',
		purchaseCount: '112',
		warehousingAmount: '198,234',
		supplierCount: '31',
		productTypes: '52',
		completionRate: '78',
	},
	{
		title: '采购团队D',
		purchaseCount: '95',
		warehousingAmount: '167,890',
		supplierCount: '26',
		productTypes: '41',
		completionRate: '85',
	},
	{
		title: '采购团队E',
		purchaseCount: '78',
		warehousingAmount: '123,456',
		supplierCount: '19',
		productTypes: '33',
		completionRate: '90',
	},
]);
</script>

<style scoped lang="scss">
.storage_Css {
	height: 100%;
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
}

.left_Css {
	display: flex;
	height: 100%;
	gap: 20px;
	padding: 10px;
	box-sizing: border-box;
}

.section_container {
	flex: 1;
	display: flex;
	flex-direction: column;
	border: 2px solid #ff6b6b;
	border-radius: 8px;
	padding: 15px;
	background-color: #fff;
	box-sizing: border-box;
}

.section_title {
	text-align: center;
	font-size: 18px;
	font-weight: bold;
	color: #ff6b6b;
	margin-bottom: 15px;
	padding-bottom: 10px;
	border-bottom: 1px solid #ff6b6b;
}

.data_container {
	flex: 1;
	overflow-y: auto;
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 10px;
	align-content: start;
}

.team_card {
	border: 1px solid #ff6b6b;
	border-radius: 6px;
	padding: 12px;
	background-color: #fafafa;
	transition: all 0.3s ease;

	&:hover {
		background-color: #f0f0f0;
		box-shadow: 0 2px 8px rgba(255, 107, 107, 0.2);
	}
}

.team_title {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-bottom: 10px;
	text-align: center;
	padding-bottom: 8px;
	border-bottom: 1px solid #eee;
}

.team_metrics {
	display: flex;
	flex-direction: column;
	gap: 6px;
}

.metric_item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 4px 0;
}

.metric_label {
	font-size: 14px;
	color: #666;
	flex: 1;
}

.metric_value {
	font-size: 14px;
	font-weight: 500;
	color: #333;
	text-align: right;
}

/* Responsive design */
@media (max-width: 768px) {
	.left_Css {
		flex-direction: column;
		gap: 15px;
	}

	.section_container {
		min-height: 300px;
	}

	.data_container {
		grid-template-columns: repeat(2, 1fr);
	}
}

@media (max-width: 480px) {
	.data_container {
		grid-template-columns: 1fr;
	}
}
</style>
