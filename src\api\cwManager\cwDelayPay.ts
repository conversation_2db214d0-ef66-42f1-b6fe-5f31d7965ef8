import request from '/@/utils/yhrequest';
import judgePort from '/@/utils/judgePort';
const apiPrefix = `${judgePort() + import.meta.env.VITE_APP_BASE_API_CwManage}/CwDelayPay/`;

// 编码列表
export const GetCwDelayPaySupplierInfoList = (params: any) => request.post(apiPrefix + 'GetCwDelayPaySupplierInfoList', params);

// 采购单数据列表
export const GetCwDelayPayPurchaseInfoList = (params: any) => request.post(apiPrefix + 'GetCwDelayPayPurchaseInfoList', params);

// 导出采购单数据
export const ExportCwDelayPayPurchaseInfo = (params: any) => request.post(apiPrefix + 'ExportCwDelayPayPurchaseInfo', params);

// 获取采购单结算退货信息
export const GetCwDelayPayPurchaseBalanceReturnInfoList = (params: any) => request.post(apiPrefix + 'GetCwDelayPayPurchaseBalanceReturnInfoList', params);

// 获取采购单结算入库信息
export const GetCwDelayPayPurchaseInWareInfoList = (params: any) => request.post(apiPrefix + 'GetCwDelayPayPurchaseInWareInfoList', params);

// 明细数据列表
export const GetCwDelayPaySupplierInfoDtlList = (params: any) => request.post(apiPrefix + 'GetCwDelayPaySupplierInfoDtlList', params);

// 导出明细数据列表
export const ExportCwDelayPaySupplierInfoDtl = (params: any) => request.post(apiPrefix + 'ExportCwDelayPaySupplierInfoDtl', params);

// 结算单数据列表
export const GetCwDelayPaySupplierBalanceRecordList = (params: any) => request.post(apiPrefix + 'GetCwDelayPaySupplierBalanceRecordList', params);

// 导出结算单数据列表
export const ExportCwDelayPaySupplierBalanceRecord = (params: any) => request.post(apiPrefix + 'ExportCwDelayPaySupplierBalanceRecord', params);

// 结算单数据明细
export const GetCwDelayPaySupplierBalanceRecord = (params: any) => request.post(apiPrefix + 'GetCwDelayPaySupplierBalanceRecord', params);

// 获取主结算信息配置
export const GetCwDelayPaySupplierBalanceConfigAsync = (params: any) => request.post(apiPrefix + 'GetCwDelayPaySupplierBalanceConfigAsync', params);

// 添加or修改主结算信息配置
export const AddOrUpdateCwDelayPaySupplierBalanceConfigAsync = (params: any) => request.post(apiPrefix + 'AddOrUpdateCwDelayPaySupplierBalanceConfigAsync', params);

// 获取发起结算单审批详情
export const GetCwDelayPaySupplierBalanceRecordApplyData = (params: any) => request.post(apiPrefix + 'GetCwDelayPaySupplierBalanceRecordApplyData', params);

// 发起结算单审批
export const SendCwDelayPaySupplierBalanceRecordApply = (params: any) => request.post(apiPrefix + 'SendCwDelayPaySupplierBalanceRecordApply', params);

// 保存结算单审批数据
export const SendCwDelayPaySupplierBalanceRecordApplyData = (params: any) => request.post(apiPrefix + 'SendCwDelayPaySupplierBalanceRecordApplyData', params);

// 手动创建结算单
export const CreateCwDelayPayBalance = (params: any) => request.post(apiPrefix + 'CreateCwDelayPayBalance', params);

// 创建调整单
export const CreateCwDelayPayAdjust = (params: any) => request.post(apiPrefix + 'CreateCwDelayPayAdjust', params);

// 作废结算单
export const CancelCwDelayPayBalance = (params: any) => request.post(apiPrefix + 'CancelCwDelayPayBalance', params);

// 获取结算单供应商列表
export const GetCwDelayPaySupplier = (params: any) => request.post(apiPrefix + 'GetCwDelayPaySupplier', params);
