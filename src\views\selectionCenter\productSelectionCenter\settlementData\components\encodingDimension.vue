<template>
	<Container>
		<template #header>
			<div class="topCss">
				<div class="publicCss">
					<manyInput v-model:inputt="query.supplierNames" :title="'供应商名称'" :verifyNumber="false" :placeholder="'请输入供应商名称'" :maxRows="100" :maxlength="1000" />
				</div>
				<div class="publicCss">
					<manyInput v-model:inputt="query.goodsCodes" :title="'商品编码'" :verifyNumber="false" :placeholder="'请输入商品编码'" :maxRows="100" :maxlength="1000" />
				</div>
				<div class="pb5">
					<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable ref="table" id="202506261839" :tableCols="tableCols" showsummary isIndexFixed :query="query" :query-api="GetCwDelayPaySupplierInfoList"> </vxetable>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent } from 'vue';
import { GetCwDelayPaySupplierInfoList } from '/@/api/cwManager/cwDelayPay';
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const manyInput = defineAsyncComponent(() => import('/@/components/yhCom/manyInput.vue'));
const emits = defineEmits(['handleQuery']);
const query = ref({
	supplierNames: '', //供应商名称
	goodsCodes: '', //商品编码
});
const table = ref();

const detail = (row: any) => {
	emits('handleQuery', row);
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'supplierName', title: '供应商名称', width: '250', align: 'center' },
	{ sortable: true, field: 'goodsCode', title: '商品编码', align: 'center', width: '220' },
	{ field: 'goodsName', title: '商品名称', align: 'center', width: '220' },
	{ field: 'brandName', title: '采购人员', align: 'center' },
	{ sortable: true, field: 'inWareCount', title: '入库总数量', formatter: 'fmtAmt0', align: 'right' },
	{ sortable: true, field: 'saleCount', title: '编码总数量', formatter: 'fmtAmt0', align: 'right' },
	{ sortable: true, field: 'waitBalanceCount', title: '当前未计算动销数量', formatter: 'fmtAmt0', align: 'right' },
	{ sortable: true, field: 'adjustCount', title: '总调整', align: 'right' },
	{ sortable: true, field: 'returnCount', title: '总退货', align: 'right' },
	{
		title: '操作',
		align: 'center',
		width: '80',
		type: 'btnList',
		field: '20250622105050',
		minWidth: '80',
		btnList: [{ title: '明细', handle: (row: any) => detail(row) }],
		fixed: 'right',
	},
]);
</script>

<style scoped lang="scss"></style>
