<template>
	<Container>
		<template #header>
			<div class="topCss">
				<dataRange
					class="publicCss"
					v-model:startDate="query.checkDateStart"
					v-model:endDate="query.checkDateEnd"
					:startPlaceholder="'采购单审核开始日期'"
					:endPlaceholder="'采购单审核结束日期'"
					style="width: 300px"
				/>
				<div class="publicCss">
					<manyInput v-model:inputt="query.supplierNames" :title="'供应商名称'" :verifyNumber="false" :placeholder="'请输入供应商名称'" :maxRows="100" :maxlength="1000" />
				</div>
				<div class="publicCss">
					<manyInput v-model:inputt="query.buyNos" :title="'采购单号'" :verifyNumber="false" :placeholder="'请输入采购单号'" :maxRows="100" :maxlength="1000" />
				</div>
				<div class="publicCss">
					<manyInput v-model:inputt="query.goodsCodes" :title="'商品编码'" :verifyNumber="false" :placeholder="'请输入商品编码'" :maxRows="100" :maxlength="1000" />
				</div>
				<div class="pb5">
					<el-button type="primary" @click="table.refreshTable(true)">查询</el-button>
				</div>
			</div>
		</template>
		<template #content>
			<vxetable
				ref="table"
				id="202506241130"
				:tableCols="tableCols"
				showsummary
				isIndexFixed
				:query="query"
				:query-api="GetCwDelayPayPurchaseInfoList"
				:export-api="ExportCwDelayPayPurchaseInfo"
				:asyncExport="{
					title: `采购单维度数据信息 ${dayjs().format('YYYY-MM-DD HH:mm:ss')}`,
					isAsync: true,
				}"
			></vxetable>
			<el-dialog title="明细数据" v-model="detailsVisible" width="60%" draggable overflow>
				<vxetable
					v-if="detailsVisible"
					ref="table1"
					id="202506221157"
					:tableCols="tableColDetails"
					showsummary
					isIndexFixed
					:query="detailsQuery"
					:query-api="storageEntry == '已入库数量' ? GetCwDelayPayPurchaseInWareInfoList : GetCwDelayPayPurchaseBalanceReturnInfoList"
					height="500"
					:isNeedPager="false"
				></vxetable>
			</el-dialog>
		</template>
	</Container>
</template>

<script setup lang="ts" name="">
import { ref, defineAsyncComponent } from 'vue';
import { GetCwDelayPayPurchaseInfoList, GetCwDelayPayPurchaseBalanceReturnInfoList, GetCwDelayPayPurchaseInWareInfoList, ExportCwDelayPayPurchaseInfo } from '/@/api/cwManager/cwDelayPay';
import dayjs from 'dayjs';
const dataRange = defineAsyncComponent(() => import('/@/components/yhCom/dateRange.vue'));
const vxetable = defineAsyncComponent(() => import('/@/components/vxeTable/vxeTable_notSummaryFmt.vue'));
const manyInput = defineAsyncComponent(() => import('/@/components/yhCom/manyInput.vue'));
const query = ref({
	checkDateStart: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
	checkDateEnd: dayjs().format('YYYY-MM-DD'),
	supplierNames: '', //供应商名称
	buyNos: '', //采购单号
	goodsCodes: '', //商品编码
});
const table = ref();
const storageEntry = ref();
const detailsVisible = ref(false);
const detailsQuery = ref({
	buyNo: null,
	goodsCodes: null,
});

// 原始详情列定义，保持不变
const originalTableColDetails: VxeTable.Columns[] = [
	{ field: 'billTime', title: '入库时间' },
	{ field: 'inWareCount', title: '入库数量', align: 'right', formatter: (row: any) => (!row.inWareCount ? '0' : row.inWareCount.toFixed(0)) },
	{ field: 'wareAmount', title: '入库金额', align: 'right', formatter: (row: any) => (!row.wareAmount ? '0.00' : row.wareAmount.toFixed(2)) },

	{ field: 'businessName', title: '类型', align: 'center' },
	{ field: 'businessNo', title: '单号', align: 'center' },
	{ field: 'returnCount', title: '退货数量', align: 'right', formatter: (row: any) => (!row.returnCount ? '0' : row.returnCount.toFixed(0)) },
	{ field: 'balanceCount', title: '结算数量', align: 'right', formatter: (row: any) => (!row.balanceCount ? '0' : row.balanceCount.toFixed(0)) },
	{ field: 'balanceAmount', title: '结算金额', align: 'right', formatter: (row: any) => (!row.balanceAmount ? '0.00' : row.balanceAmount.toFixed(2)) },
];

// 当前展示给表格的列，初始化为原始列的拷贝
const tableColDetails = ref<VxeTable.Columns[]>([...originalTableColDetails]);

const detailedMethod = (row: any, type: string) => {
	// 需要排除的列 field，更稳妥
	const excludedColumns = type === '已入库数量' ? ['businessName', 'businessNo', 'returnCount', 'balanceCount', 'balanceAmount'] : ['billTime', 'inWareCount', 'wareAmount'];
	storageEntry.value = type;
	// 每次点击都基于原始列重新过滤，避免多个点击后列缺失
	tableColDetails.value = originalTableColDetails.filter((item) => item.field && !excludedColumns.includes(item.field as string));

	detailsVisible.value = true;
	detailsQuery.value.buyNo = row.buyNo;
	detailsQuery.value.goodsCodes = row.goodsCode;
};

const tableCols = ref<VxeTable.Columns[]>([
	{ sortable: true, field: 'checkDate', title: '采购单审核日期', width: '135' },
	{ sortable: true, field: 'supplierName', title: '供应商名称', width: '230', align: 'center' },
	{ sortable: true, field: 'buyNo', title: '采购单号', align: 'center' },
	{ sortable: true, field: 'goodsCode', title: '商品编码', width: '220', align: 'center' },
	{ sortable: true, field: 'cost', title: '成本价', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'inWareCount', title: '已入库数量', formatter: 'fmtAmt0', align: 'right' },
	{ sortable: true, field: 'inWareAmount', title: '已入库金额', formatter: 'fmtAmt2', align: 'right' },
	{ sortable: true, field: 'lastInWareTime', title: '入库时间', width: '135', align: 'center', type: 'click', handle: (row: any) => detailedMethod(row, '已入库数量') },
	{ sortable: true, field: 'waitInWareCount', title: '未入库数量', formatter: 'fmtAmt0', align: 'right' },
	{
		sortable: true,
		field: 'billId',
		title: '结算/退货单号',
		width: '150',
		align: 'center',
		type: 'click',
		formatter: (row: any) => row.billId || '',
		handle: (row: any) => detailedMethod(row, '退货单号'),
	},
	{
		sortable: true,
		field: 'returnCount',
		title: '退货数量',
		align: 'right',
		type: 'click',
		formatter: (row: any) => (!row.returnCount ? '0' : row.returnCount),
		handle: (row: any) => detailedMethod(row, '退货数量'),
	},
	{
		sortable: true,
		field: 'balanceCount',
		title: '结算数量',
		align: 'right',
		type: 'click',
		formatter: (row: any) => (!row.balanceCount ? '0' : row.balanceCount),
		handle: (row: any) => detailedMethod(row, '结算数量'),
	},
	{
		sortable: true,
		field: 'balanceAmount',
		title: '结算金额',
		align: 'right',
		type: 'click',
		formatter: (row: any) => (!row.balanceAmount ? '0' : row.balanceAmount),
		handle: (row: any) => detailedMethod(row, '结算金额'),
	},
]);
</script>

<style scoped lang="scss"></style>
